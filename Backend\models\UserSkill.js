const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserSkill = sequelize.define('UserSkill', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  skillId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'skills',
      key: 'id'
    }
  },
  proficiencyLevel: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced', 'expert'),
    allowNull: false,
    defaultValue: 'beginner'
  },
  yearsOfExperience: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 50
    }
  },
  isTeaching: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the user is willing to teach this skill'
  },
  isLearning: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the user wants to learn this skill'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    validate: {
      len: [0, 500]
    }
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'user_skills',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['userId', 'skillId']
    },
    {
      fields: ['isTeaching']
    },
    {
      fields: ['isLearning']
    },
    {
      fields: ['proficiencyLevel']
    }
  ]
});

module.exports = UserSkill;
