import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faEye,
  faPlus,
  faSort,
  faSortUp,
  faSortDown,
  faCheckCircle,
  faTimesCircle
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const UsersContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 1.8rem;
  color: #333;
  margin: 0;
`;

const ActionButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #5a3cc0;
  }
`;

const FilterSection = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const SearchInput = styled.div`
  flex: 1;
  position: relative;
  
  input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
    }
  }
  
  svg {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
  }
`;

const FilterButton = styled.button`
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
`;

const TableCard = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  background-color: #f8f9fa;
  
  th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
  }
`;

const TableBody = styled.tbody`
  tr {
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
  }
  
  td {
    padding: 15px;
    color: #555;
  }
`;

const SortIcon = styled.span`
  margin-left: 5px;
  display: inline-block;
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f1f3f5;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const UserName = styled.div`
  font-weight: 500;
  color: #333;
`;

const UserEmail = styled.div`
  font-size: 0.8rem;
  color: #777;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  
  background-color: ${props => {
    switch(props.status) {
      case 'active': return '#d4edda';
      case 'inactive': return '#f8d7da';
      case 'pending': return '#fff3cd';
      default: return '#e2e3e5';
    }
  }};
  
  color: ${props => {
    switch(props.status) {
      case 'active': return '#155724';
      case 'inactive': return '#721c24';
      case 'pending': return '#856404';
      default: return '#383d41';
    }
  }};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.color || '#6c757d'};
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: ${props => {
      switch(props.color) {
        case '#dc3545': return '#c82333';
        case '#28a745': return '#218838';
        case '#007bff': return '#0069d9';
        default: return '#5a6268';
      }
    }};
  }
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-top: 1px solid #eee;
`;

const PageInfo = styled.div`
  font-size: 0.9rem;
  color: #6c757d;
`;

const PageButtons = styled.div`
  display: flex;
  gap: 5px;
`;

const PageButton = styled.button`
  background-color: ${props => props.active ? 'var(--primary-color)' : 'white'};
  color: ${props => props.active ? 'white' : '#6c757d'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : '#ddd'};
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : '#f8f9fa'};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Users = () => {
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  
  // Mock data for users
  const users = [
    {
      id: 1,
      name: 'John Smith',
      email: '<EMAIL>',
      role: 'Student',
      status: 'active',
      joinDate: '2023-01-15',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    {
      id: 2,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'Teacher',
      status: 'active',
      joinDate: '2023-02-20',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
    },
    {
      id: 3,
      name: 'Michael Brown',
      email: '<EMAIL>',
      role: 'Student',
      status: 'inactive',
      joinDate: '2023-03-10',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
    },
    {
      id: 4,
      name: 'Emily Davis',
      email: '<EMAIL>',
      role: 'Teacher',
      status: 'active',
      joinDate: '2023-04-05',
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg'
    },
    {
      id: 5,
      name: 'David Wilson',
      email: '<EMAIL>',
      role: 'Student',
      status: 'pending',
      joinDate: '2023-05-12',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg'
    },
    {
      id: 6,
      name: 'Jennifer Taylor',
      email: '<EMAIL>',
      role: 'Teacher',
      status: 'active',
      joinDate: '2023-06-18',
      avatar: 'https://randomuser.me/api/portraits/women/6.jpg'
    },
    {
      id: 7,
      name: 'Robert Anderson',
      email: '<EMAIL>',
      role: 'Student',
      status: 'active',
      joinDate: '2023-07-22',
      avatar: 'https://randomuser.me/api/portraits/men/7.jpg'
    },
    {
      id: 8,
      name: 'Lisa Martinez',
      email: '<EMAIL>',
      role: 'Teacher',
      status: 'inactive',
      joinDate: '2023-08-30',
      avatar: 'https://randomuser.me/api/portraits/women/8.jpg'
    }
  ];
  
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  const getSortIcon = (field) => {
    if (sortField !== field) return <FontAwesomeIcon icon={faSort} />;
    return sortDirection === 'asc' ? <FontAwesomeIcon icon={faSortUp} /> : <FontAwesomeIcon icon={faSortDown} />;
  };
  
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  return (
    <UsersContainer>
      <PageHeader>
        <PageTitle>User Management</PageTitle>
        <ActionButton>
          <FontAwesomeIcon icon={faPlus} />
          Add New User
        </ActionButton>
      </PageHeader>
      
      <FilterSection>
        <SearchInput>
          <FontAwesomeIcon icon={faSearch} />
          <input type="text" placeholder="Search users..." />
        </SearchInput>
        <FilterButton>
          <FontAwesomeIcon icon={faFilter} />
          Filters
        </FilterButton>
      </FilterSection>
      
      <TableCard>
        <Table>
          <TableHead>
            <tr>
              <th onClick={() => handleSort('name')}>
                User
                <SortIcon>{getSortIcon('name')}</SortIcon>
              </th>
              <th onClick={() => handleSort('role')}>
                Role
                <SortIcon>{getSortIcon('role')}</SortIcon>
              </th>
              <th onClick={() => handleSort('status')}>
                Status
                <SortIcon>{getSortIcon('status')}</SortIcon>
              </th>
              <th onClick={() => handleSort('joinDate')}>
                Join Date
                <SortIcon>{getSortIcon('joinDate')}</SortIcon>
              </th>
              <th>Actions</th>
            </tr>
          </TableHead>
          <TableBody>
            {users.map(user => (
              <tr key={user.id}>
                <td>
                  <UserInfo>
                    <UserAvatar>
                      <img src={user.avatar} alt={user.name} />
                    </UserAvatar>
                    <div>
                      <UserName>{user.name}</UserName>
                      <UserEmail>{user.email}</UserEmail>
                    </div>
                  </UserInfo>
                </td>
                <td>{user.role}</td>
                <td>
                  <StatusBadge status={user.status}>
                    {user.status === 'active' && <FontAwesomeIcon icon={faCheckCircle} style={{ marginRight: '5px' }} />}
                    {user.status === 'inactive' && <FontAwesomeIcon icon={faTimesCircle} style={{ marginRight: '5px' }} />}
                    {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                  </StatusBadge>
                </td>
                <td>{formatDate(user.joinDate)}</td>
                <td>
                  <ActionButtons>
                    <IconButton color="#007bff" title="View">
                      <FontAwesomeIcon icon={faEye} />
                    </IconButton>
                    <IconButton color="#28a745" title="Edit">
                      <FontAwesomeIcon icon={faEdit} />
                    </IconButton>
                    <IconButton color="#dc3545" title="Delete">
                      <FontAwesomeIcon icon={faTrash} />
                    </IconButton>
                  </ActionButtons>
                </td>
              </tr>
            ))}
          </TableBody>
        </Table>
        <Pagination>
          <PageInfo>Showing 1 to 8 of 8 entries</PageInfo>
          <PageButtons>
            <PageButton disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
              Previous
            </PageButton>
            <PageButton active={currentPage === 1} onClick={() => setCurrentPage(1)}>
              1
            </PageButton>
            <PageButton onClick={() => setCurrentPage(2)}>
              2
            </PageButton>
            <PageButton onClick={() => setCurrentPage(3)}>
              3
            </PageButton>
            <PageButton onClick={() => setCurrentPage(currentPage + 1)}>
              Next
            </PageButton>
          </PageButtons>
        </Pagination>
      </TableCard>
    </UsersContainer>
  );
};

export default Users;
