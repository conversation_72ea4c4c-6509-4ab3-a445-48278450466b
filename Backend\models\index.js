const User = require('./User');
const Skill = require('./Skill');
const UserSkill = require('./UserSkill');
const SkillExchange = require('./SkillExchange');

// Define associations

// User and Skill many-to-many relationship through UserSkill
User.belongsToMany(Skill, {
  through: UserSkill,
  foreignKey: 'userId',
  otherKey: 'skillId',
  as: 'skills'
});

Skill.belongsToMany(User, {
  through: UserSkill,
  foreignKey: 'skillId',
  otherKey: 'userId',
  as: 'users'
});

// Direct associations for UserSkill
User.hasMany(UserSkill, {
  foreignKey: 'userId',
  as: 'userSkills'
});

UserSkill.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

Skill.hasMany(UserSkill, {
  foreignKey: 'skillId',
  as: 'userSkills'
});

UserSkill.belongsTo(Skill, {
  foreignKey: 'skillId',
  as: 'skill'
});

// SkillExchange associations
User.hasMany(SkillExchange, {
  foreignKey: 'requesterId',
  as: 'requestedExchanges'
});

User.hasMany(SkillExchange, {
  foreignKey: 'providerId',
  as: 'providedExchanges'
});

SkillExchange.belongsTo(User, {
  foreignKey: 'requesterId',
  as: 'requester'
});

SkillExchange.belongsTo(User, {
  foreignKey: 'providerId',
  as: 'provider'
});

Skill.hasMany(SkillExchange, {
  foreignKey: 'requestedSkillId',
  as: 'requestedExchanges'
});

Skill.hasMany(SkillExchange, {
  foreignKey: 'offeredSkillId',
  as: 'offeredExchanges'
});

SkillExchange.belongsTo(Skill, {
  foreignKey: 'requestedSkillId',
  as: 'requestedSkill'
});

SkillExchange.belongsTo(Skill, {
  foreignKey: 'offeredSkillId',
  as: 'offeredSkill'
});

module.exports = {
  User,
  Skill,
  UserSkill,
  SkillExchange
};
