import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faEye,
  faSort,
  faSortUp,
  faSortDown,
  faCheckCircle,
  faTimesCircle,
  faExchangeAlt,
  faCalendarAlt,
  faUser,
  faUsers
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const ExchangesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 1.8rem;
  color: #333;
  margin: 0;
`;

const FilterSection = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const SearchInput = styled.div`
  flex: 1;
  position: relative;
  
  input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
    }
  }
  
  svg {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
  }
`;

const FilterButton = styled.button`
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
`;

const StatusFilters = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const StatusFilter = styled.button`
  background-color: ${props => props.active ? 'var(--primary-color)' : 'white'};
  color: ${props => props.active ? 'white' : '#555'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : '#ddd'};
  border-radius: 50px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : '#f8f9fa'};
  }
`;

const TableCard = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  background-color: #f8f9fa;
  
  th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
  }
`;

const TableBody = styled.tbody`
  tr {
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
  }
  
  td {
    padding: 15px;
    color: #555;
  }
`;

const SortIcon = styled.span`
  margin-left: 5px;
  display: inline-block;
`;

const ExchangeInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const ExchangeIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: ${props => props.bgColor || '#f1f3f5'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
`;

const ExchangeTitle = styled.div`
  font-weight: 500;
  color: #333;
`;

const ExchangeDetails = styled.div`
  font-size: 0.8rem;
  color: #777;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const UserAvatar = styled.div`
  width: 30px;
  height: 30px;
  border-radius: 50%;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const UserName = styled.div`
  font-weight: 500;
  color: #333;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  
  background-color: ${props => {
    switch(props.status) {
      case 'completed': return '#d4edda';
      case 'in-progress': return '#cce5ff';
      case 'scheduled': return '#fff3cd';
      case 'cancelled': return '#f8d7da';
      default: return '#e2e3e5';
    }
  }};
  
  color: ${props => {
    switch(props.status) {
      case 'completed': return '#155724';
      case 'in-progress': return '#004085';
      case 'scheduled': return '#856404';
      case 'cancelled': return '#721c24';
      default: return '#383d41';
    }
  }};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.color || '#6c757d'};
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: ${props => {
      switch(props.color) {
        case '#dc3545': return '#c82333';
        case '#28a745': return '#218838';
        case '#007bff': return '#0069d9';
        default: return '#5a6268';
      }
    }};
  }
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-top: 1px solid #eee;
`;

const PageInfo = styled.div`
  font-size: 0.9rem;
  color: #6c757d;
`;

const PageButtons = styled.div`
  display: flex;
  gap: 5px;
`;

const PageButton = styled.button`
  background-color: ${props => props.active ? 'var(--primary-color)' : 'white'};
  color: ${props => props.active ? 'white' : '#6c757d'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : '#ddd'};
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : '#f8f9fa'};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Exchanges = () => {
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [activeStatus, setActiveStatus] = useState('all');
  
  // Status filters
  const statusFilters = [
    { id: 'all', name: 'All Exchanges' },
    { id: 'completed', name: 'Completed' },
    { id: 'in-progress', name: 'In Progress' },
    { id: 'scheduled', name: 'Scheduled' },
    { id: 'cancelled', name: 'Cancelled' }
  ];
  
  // Mock data for exchanges
  const exchanges = [
    {
      id: 1,
      title: 'JavaScript Tutoring',
      skillCategory: 'Programming',
      teacher: {
        id: 101,
        name: 'John Smith',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
      },
      student: {
        id: 102,
        name: 'Emily Davis',
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
      },
      status: 'completed',
      date: '2023-04-15',
      duration: '5 sessions',
      icon: faExchangeAlt,
      color: '#4c6ef5'
    },
    {
      id: 2,
      title: 'Piano Lessons',
      skillCategory: 'Music',
      teacher: {
        id: 103,
        name: 'Sarah Johnson',
        avatar: 'https://randomuser.me/api/portraits/women/3.jpg'
      },
      student: {
        id: 104,
        name: 'Michael Brown',
        avatar: 'https://randomuser.me/api/portraits/men/4.jpg'
      },
      status: 'in-progress',
      date: '2023-05-20',
      duration: '8 sessions',
      icon: faExchangeAlt,
      color: '#fd7e14'
    },
    {
      id: 3,
      title: 'Spanish Language',
      skillCategory: 'Language',
      teacher: {
        id: 105,
        name: 'David Wilson',
        avatar: 'https://randomuser.me/api/portraits/men/5.jpg'
      },
      student: {
        id: 106,
        name: 'Jennifer Taylor',
        avatar: 'https://randomuser.me/api/portraits/women/6.jpg'
      },
      status: 'scheduled',
      date: '2023-06-10',
      duration: '10 sessions',
      icon: faExchangeAlt,
      color: '#20c997'
    },
    {
      id: 4,
      title: 'Yoga Instruction',
      skillCategory: 'Health & Fitness',
      teacher: {
        id: 107,
        name: 'Lisa Martinez',
        avatar: 'https://randomuser.me/api/portraits/women/7.jpg'
      },
      student: {
        id: 108,
        name: 'Robert Anderson',
        avatar: 'https://randomuser.me/api/portraits/men/8.jpg'
      },
      status: 'cancelled',
      date: '2023-03-05',
      duration: '6 sessions',
      icon: faExchangeAlt,
      color: '#dc3545'
    },
    {
      id: 5,
      title: 'Digital Marketing',
      skillCategory: 'Business',
      teacher: {
        id: 109,
        name: 'Thomas Clark',
        avatar: 'https://randomuser.me/api/portraits/men/9.jpg'
      },
      student: {
        id: 110,
        name: 'Amanda White',
        avatar: 'https://randomuser.me/api/portraits/women/10.jpg'
      },
      status: 'completed',
      date: '2023-02-18',
      duration: '4 sessions',
      icon: faExchangeAlt,
      color: '#4c6ef5'
    },
    {
      id: 6,
      title: 'UI/UX Design',
      skillCategory: 'Design',
      teacher: {
        id: 111,
        name: 'Jessica Lee',
        avatar: 'https://randomuser.me/api/portraits/women/11.jpg'
      },
      student: {
        id: 112,
        name: 'Daniel Harris',
        avatar: 'https://randomuser.me/api/portraits/men/12.jpg'
      },
      status: 'in-progress',
      date: '2023-05-30',
      duration: '7 sessions',
      icon: faExchangeAlt,
      color: '#fd7e14'
    },
    {
      id: 7,
      title: 'Italian Cooking',
      skillCategory: 'Cooking',
      teacher: {
        id: 113,
        name: 'Maria Rodriguez',
        avatar: 'https://randomuser.me/api/portraits/women/13.jpg'
      },
      student: {
        id: 114,
        name: 'James Wilson',
        avatar: 'https://randomuser.me/api/portraits/men/14.jpg'
      },
      status: 'scheduled',
      date: '2023-06-25',
      duration: '3 sessions',
      icon: faExchangeAlt,
      color: '#20c997'
    },
    {
      id: 8,
      title: 'Photography Basics',
      skillCategory: 'Photography',
      teacher: {
        id: 115,
        name: 'Christopher Martin',
        avatar: 'https://randomuser.me/api/portraits/men/15.jpg'
      },
      student: {
        id: 116,
        name: 'Olivia Thompson',
        avatar: 'https://randomuser.me/api/portraits/women/16.jpg'
      },
      status: 'completed',
      date: '2023-04-08',
      duration: '5 sessions',
      icon: faExchangeAlt,
      color: '#4c6ef5'
    }
  ];
  
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  const getSortIcon = (field) => {
    if (sortField !== field) return <FontAwesomeIcon icon={faSort} />;
    return sortDirection === 'asc' ? <FontAwesomeIcon icon={faSortUp} /> : <FontAwesomeIcon icon={faSortDown} />;
  };
  
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Filter exchanges by status
  const filteredExchanges = activeStatus === 'all' 
    ? exchanges 
    : exchanges.filter(exchange => exchange.status === activeStatus);
  
  return (
    <ExchangesContainer>
      <PageHeader>
        <PageTitle>Exchange Management</PageTitle>
      </PageHeader>
      
      <FilterSection>
        <SearchInput>
          <FontAwesomeIcon icon={faSearch} />
          <input type="text" placeholder="Search exchanges..." />
        </SearchInput>
        <FilterButton>
          <FontAwesomeIcon icon={faFilter} />
          Filters
        </FilterButton>
      </FilterSection>
      
      <StatusFilters>
        {statusFilters.map(filter => (
          <StatusFilter
            key={filter.id}
            active={activeStatus === filter.id}
            onClick={() => setActiveStatus(filter.id)}
          >
            {filter.id === 'completed' && <FontAwesomeIcon icon={faCheckCircle} />}
            {filter.id === 'in-progress' && <FontAwesomeIcon icon={faExchangeAlt} />}
            {filter.id === 'scheduled' && <FontAwesomeIcon icon={faCalendarAlt} />}
            {filter.id === 'cancelled' && <FontAwesomeIcon icon={faTimesCircle} />}
            {filter.id === 'all' && <FontAwesomeIcon icon={faUsers} />}
            {filter.name}
          </StatusFilter>
        ))}
      </StatusFilters>
      
      <TableCard>
        <Table>
          <TableHead>
            <tr>
              <th onClick={() => handleSort('title')}>
                Exchange
                <SortIcon>{getSortIcon('title')}</SortIcon>
              </th>
              <th onClick={() => handleSort('teacher')}>
                Teacher
                <SortIcon>{getSortIcon('teacher')}</SortIcon>
              </th>
              <th onClick={() => handleSort('student')}>
                Student
                <SortIcon>{getSortIcon('student')}</SortIcon>
              </th>
              <th onClick={() => handleSort('status')}>
                Status
                <SortIcon>{getSortIcon('status')}</SortIcon>
              </th>
              <th onClick={() => handleSort('date')}>
                Date
                <SortIcon>{getSortIcon('date')}</SortIcon>
              </th>
              <th>Actions</th>
            </tr>
          </TableHead>
          <TableBody>
            {filteredExchanges.map(exchange => (
              <tr key={exchange.id}>
                <td>
                  <ExchangeInfo>
                    <ExchangeIcon bgColor={exchange.color}>
                      <FontAwesomeIcon icon={exchange.icon} />
                    </ExchangeIcon>
                    <div>
                      <ExchangeTitle>{exchange.title}</ExchangeTitle>
                      <ExchangeDetails>{exchange.skillCategory} • {exchange.duration}</ExchangeDetails>
                    </div>
                  </ExchangeInfo>
                </td>
                <td>
                  <UserInfo>
                    <UserAvatar>
                      <img src={exchange.teacher.avatar} alt={exchange.teacher.name} />
                    </UserAvatar>
                    <UserName>{exchange.teacher.name}</UserName>
                  </UserInfo>
                </td>
                <td>
                  <UserInfo>
                    <UserAvatar>
                      <img src={exchange.student.avatar} alt={exchange.student.name} />
                    </UserAvatar>
                    <UserName>{exchange.student.name}</UserName>
                  </UserInfo>
                </td>
                <td>
                  <StatusBadge status={exchange.status}>
                    {exchange.status === 'completed' && <FontAwesomeIcon icon={faCheckCircle} style={{ marginRight: '5px' }} />}
                    {exchange.status === 'in-progress' && <FontAwesomeIcon icon={faExchangeAlt} style={{ marginRight: '5px' }} />}
                    {exchange.status === 'scheduled' && <FontAwesomeIcon icon={faCalendarAlt} style={{ marginRight: '5px' }} />}
                    {exchange.status === 'cancelled' && <FontAwesomeIcon icon={faTimesCircle} style={{ marginRight: '5px' }} />}
                    {exchange.status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                  </StatusBadge>
                </td>
                <td>{formatDate(exchange.date)}</td>
                <td>
                  <ActionButtons>
                    <IconButton color="#007bff" title="View">
                      <FontAwesomeIcon icon={faEye} />
                    </IconButton>
                    <IconButton color="#28a745" title="Edit">
                      <FontAwesomeIcon icon={faEdit} />
                    </IconButton>
                    <IconButton color="#dc3545" title="Delete">
                      <FontAwesomeIcon icon={faTrash} />
                    </IconButton>
                  </ActionButtons>
                </td>
              </tr>
            ))}
          </TableBody>
        </Table>
        <Pagination>
          <PageInfo>Showing 1 to {filteredExchanges.length} of {filteredExchanges.length} entries</PageInfo>
          <PageButtons>
            <PageButton disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
              Previous
            </PageButton>
            <PageButton active={currentPage === 1} onClick={() => setCurrentPage(1)}>
              1
            </PageButton>
            <PageButton onClick={() => setCurrentPage(2)}>
              2
            </PageButton>
            <PageButton onClick={() => setCurrentPage(3)}>
              3
            </PageButton>
            <PageButton onClick={() => setCurrentPage(currentPage + 1)}>
              Next
            </PageButton>
          </PageButtons>
        </Pagination>
      </TableCard>
    </ExchangesContainer>
  );
};

export default Exchanges;
