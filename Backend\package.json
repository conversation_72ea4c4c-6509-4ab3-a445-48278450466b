{"name": "skillswap-backend", "version": "1.0.0", "description": "Backend API for SkillSwap application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["skillswap", "api", "backend", "nodejs", "express", "sequelize"], "author": "SkillSwap Team", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.2", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.1.10"}}