import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";
import { useAuth } from "../../context/AuthContext";
import { skillsAPI } from "../../services/api";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faGraduationCap,
  faHandshake,
  faUsers,
  faLaptopCode,
  faChalkboardTeacher,
  faArrowRight,
  faBrain,
  faRobot,
  faLink,
  faServer,
  faShieldAlt,
  faTasks,
  faComments,
  faPen,
  faVideo,
  faLightbulb,
  faExchangeAlt,
  faPeopleArrows,
  faUserFriends,
  faHandsHelping,
  faBalanceScale,
  faUniversalAccess,
  faChartLine,
  faGlobe,
} from "@fortawesome/free-solid-svg-icons";

import Hero from "../../components/ui/Hero";
import Section from "../../components/ui/Section";
import Button from "../../components/ui/Button";
import Card from "../../components/ui/Card";

// Import placeholder images
import images from "../../assets/placeholderImages";

const ServicesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const ServiceCard = styled(Card)`
  text-align: center;
  padding: var(--spacing-lg);

  h3 {
    font-size: 2.25rem;
  }

  p {
    font-size: 1.2rem;
  }
`;

const ServiceIcon = styled.div`
  font-size: 2.5rem;
  color: var(--dark-color);
  margin-bottom: var(--spacing-md);
  background-color: #f1f1f1;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  border: 1px solid #e0e0e0;
`;

const AboutContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

const AboutImage = styled.img`
  width: 100%;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  @media (max-width: 992px) {
    margin-bottom: var(--spacing-lg);
  }
`;

const AboutContent = styled.div`
  padding: var(--spacing-lg);
  background-color: #ffffff;
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

  p {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
  }
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
`;

const FeatureItem = styled.li`
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.3rem;

  svg {
    color: var(--primary-color);
    margin-right: var(--spacing-md);
  }
`;

const WhyChooseTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;
  text-align: center;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
  }

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);

    &:after {
      width: 60px;
      bottom: -10px;
    }
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const StatCard = styled.div`
  background-color: var(--light-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const StatNumber = styled.h3`
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
`;

const StatTitle = styled.p`
  font-size: 1.5rem;
  color: var(--text-color);
  font-weight: 500;
`;

// Community Feedback section styles
const FeedbackSection = styled.section`
  background-color: #f8f9fa; // Light gray background
  color: var(--text-color);
  min-height: 33vh; // Make section occupy 1/3 of viewport height
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections
`;

const FeedbackTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
  }

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);

    &:after {
      width: 60px;
      bottom: -10px;
    }
  }
`;

const TestimonialsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  width: calc(100% - 6rem);
  margin: 0 3rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
`;

const TestimonialCard = styled.div`
  background-color: white;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  border: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
`;

const TestimonialText = styled.p`
  font-style: italic;
  margin-bottom: var(--spacing-lg);
  flex-grow: 1;
  color: var(--text-color);
  line-height: 1.5;
  font-size: 1.5rem;
  position: relative;
  z-index: 1;
  text-align: left;
`;

const TestimonialAuthor = styled.div`
  display: flex;
  align-items: center;
  color: #4caf50; // Green color as shown in the image
  font-size: 1.5rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-align: left;
  align-self: flex-start;
  margin-top: auto;

  &::before {
    content: "— ";
    margin-right: var(--spacing-xs);
  }
`;

// FAQ section styles
const FAQSection = styled.section`
  background-color: #f8f9fa; // Light gray background
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections
`;

const FAQTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: #8bc34a; // Green underline as shown in the image
  }

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);

    &:after {
      width: 60px;
      bottom: -10px;
    }
  }
`;

const FAQSubtitle = styled.p`
  font-size: 1.5rem;
  color: var(--text-light);
  max-width: 700px;
  margin: 0 auto;
  margin-bottom: var(--spacing-2xl);
  margin-top: var(--spacing-xl);

  @media (max-width: 992px) {
    font-size: 1.3rem;
    max-width: 600px;
    margin-bottom: var(--spacing-xl);
  }

  @media (max-width: 768px) {
    font-size: 1.1rem;
    max-width: 500px;
  }

  @media (max-width: 576px) {
    font-size: 1rem;
    max-width: 100%;
    margin-bottom: var(--spacing-lg);
    margin-top: var(--spacing-md);
  }
`;

const FAQTabsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  width: calc(100% - 6rem);
  margin-left: 3rem;
  margin-right: 3rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: var(--spacing-sm);
  }

  @media (max-width: 576px) {
    width: calc(100% - 2rem);
    margin-left: 1rem;
    margin-right: 1rem;
  }
`;

const FAQTab = styled.button`
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 50px;
  border: none;
  background-color: ${(props) => (props.active ? "#8bc34a" : "transparent")};
  color: ${(props) => (props.active ? "white" : "var(--text-color)")};
  font-weight: ${(props) => (props.active ? "600" : "400")};
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-sm);

  &:hover {
    background-color: ${(props) =>
      props.active ? "#8bc34a" : "rgba(139, 195, 74, 0.1)"};
  }

  @media (max-width: 768px) {
    font-size: 1.2rem;
    padding: var(--spacing-xs) var(--spacing-md);
  }

  @media (max-width: 576px) {
    font-size: 1rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
  }
`;

const FAQContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: calc(100% - 6rem);
  margin: 0 3rem;

  @media (max-width: 768px) {
    gap: var(--spacing-sm);
  }

  @media (max-width: 576px) {
    width: calc(100% - 2rem);
    margin: 0 1rem;
  }
`;

const FAQItem = styled.div`
  background-color: #ffffff; // Pure white
  border-radius: var(--border-radius-md);
  overflow: hidden;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-md);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
`;

const FAQHeader = styled.div`
  padding: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: ${(props) =>
    props.isOpen ? "1px solid rgba(255, 255, 255, 0.1)" : "none"};
`;

const FAQCategory = styled.span`
  font-size: 1.5rem;
  color: ${(props) => {
    switch (props.category) {
      case "General":
        return "#8bc34a";
      case "Skill exchange":
        return "var(--primary-color)";
      case "Technical":
        return "var(--secondary-color)";
      default:
        return "var(--text-light)";
    }
  }};
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: var(--spacing-xs);
  display: block;
`;

const FAQQuestion = styled.h3`
  font-size: 1.5rem;
  color: var(--dark-color);
  margin: 0;
  font-weight: normal;
`;

const FAQToggle = styled.div`
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f1f1f1;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
`;

const FAQContent = styled.div`
  padding: ${(props) =>
    props.isOpen ? "var(--spacing-lg)" : "0 var(--spacing-lg)"};
  max-height: ${(props) => (props.isOpen ? "500px" : "0")};
  opacity: ${(props) => (props.isOpen ? "1" : "0")};
  transition: all 0.3s ease;
  overflow: hidden;
`;

const FAQAnswer = styled.p`
  color: var(--text-light);
  line-height: 1.6;
  margin: 0;
  font-size: 1.5rem;
`;

const FAQSupportContainer = styled.div`
  background-color: #ffffff; // Pure white
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl);
  text-align: center;
  margin-top: var(--spacing-lg);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
`;

const FAQSupportText = styled.p`
  color: var(--dark-color);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
`;

const FAQSupportButton = styled.button`
  background-color: #f1f1f1;
  color: var(--dark-color);
  border: 1px solid #e0e0e0;
  border-radius: 50px;
  padding: var(--spacing-sm) var(--spacing-xl);
  font-size: 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;

  &:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
`;

// Where to Start section styles
const WhereToStartSection = styled.section`
  background-color: #f8f9fa; // Light gray background
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections
`;

const WhereToStartTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: #8bc34a; // Green underline as shown in the image
  }
`;

const StepsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  width: calc(100% - 6rem);
  margin: 0 3rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
`;

const StepCard = styled.div`
  background-color: #ffffff; // Pure white
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  text-align: left;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
`;

const WhereToStartStepNumber = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f1f1f1;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  border: 1px solid #e0e0e0;
`;

const StepText = styled.p`
  color: var(--dark-color);
  margin: 0;
  font-size: 1.5rem;
`;

const TipCard = styled.div`
  background-color: #ffffff; // Pure white
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  text-align: left;
  grid-column: 2;
  grid-row: 1 / span 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  @media (max-width: 992px) {
    grid-column: 1;
    grid-row: auto;
  }
`;

const TipText = styled.p`
  color: var(--dark-color);
  margin: 0;
  font-size: 1.5rem;
`;

// Our Values section styles
const ValuesSection = styled.section`
  background-color: #f8f9fa; // Light gray background
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections
`;

const ValuesTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: #8bc34a; // Green underline as shown in the image
  }

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);

    &:after {
      width: 60px;
      bottom: -10px;
    }
  }
`;

const ValuesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  width: calc(100% - 6rem);
  margin: var(--spacing-xl) 3rem 0;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
    width: calc(100% - 2rem);
    margin: var(--spacing-lg) 1rem 0;
    gap: var(--spacing-md);
  }
`;

const ValueCard = styled.div`
  background-color: #ffffff; // Pure white
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl) var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  @media (max-width: 992px) {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  @media (max-width: 576px) {
    padding: var(--spacing-md) var(--spacing-sm);
  }
`;

const ValueIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f1f1f1;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-color);
  font-size: 2.5rem;
  border: 1px solid #e0e0e0;

  @media (max-width: 992px) {
    width: 70px;
    height: 70px;
    font-size: 2rem;
  }

  @media (max-width: 576px) {
    width: 60px;
    height: 60px;
    font-size: 1.75rem;
  }
`;

const ValueTitle = styled.h3`
  color: var(--dark-color);
  font-size: 2rem;
  margin: 0 0 var(--spacing-sm);
  font-weight: 600;
`;

const ValueDescription = styled.p`
  color: var(--text-light);
  font-size: 1.5rem;
  margin: 0;
  line-height: 1.6;
`;

const JoinNowSection = styled.section`
  background-color: #f8f9fa; // Light gray background
  padding: var(--spacing-2xl) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections

  &::before,
  &::after {
    content: "";
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(139, 195, 74, 0.1); // Light green circles
    z-index: 0;
  }

  &::before {
    top: -100px;
    right: -100px;
  }

  &::after {
    bottom: -100px;
    left: -100px;
  }
`;

const JoinNowTitle = styled.h2`
  font-size: var(--font-size-6xl);
  color: var(--dark-color);
  margin-bottom: var(--spacing-md);
  position: relative;
  z-index: 1;

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);
  }
`;

const JoinNowText = styled.p`
  font-size: 1.5rem;
  color: var(--text-light);
  margin-bottom: var(--spacing-xl);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;

  @media (max-width: 992px) {
    font-size: 1.3rem;
    max-width: 600px;
  }

  @media (max-width: 768px) {
    font-size: 1.1rem;
    max-width: 500px;
    margin-bottom: var(--spacing-lg);
  }

  @media (max-width: 576px) {
    font-size: 1rem;
    max-width: 100%;
    margin-bottom: var(--spacing-md);
  }
`;

const JoinNowButton = styled.button`
  background-color: #f1f1f1;
  color: var(--dark-color);
  border: 1px solid #e0e0e0;
  border-radius: 50px;
  padding: var(--spacing-sm) var(--spacing-xl);
  font-size: var(--font-size-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;

  &:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: var(--spacing-xs) var(--spacing-lg);
    font-size: var(--font-size-sm);
  }

  @media (max-width: 576px) {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.9rem;
  }
`;

const CTAContainer = styled.div`
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
`;

// How It Works section styles
const HowItWorksSection = styled.section`
  background-color: white; // White background
  color: var(--text-color);
  min-height: 100vh; // Make section occupy full viewport height
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle at top right,
      rgba(106, 61, 232, 0.05),
      transparent 60%
    );
    pointer-events: none;
  }
`;

const HowItWorksTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-2xl);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;
  text-transform: capitalize;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
  }

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-xl);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-lg);

    &:after {
      width: 60px;
      bottom: -10px;
    }
  }
`;

const StepsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  width: calc(100% - 6rem);
  margin: var(--spacing-2xl) 3rem 0 3rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
`;

const StepItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--light-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const StepIcon = styled.div`
  width: 80px;
  height: 80px;
  background-color: #f1f1f1; // Light gray background
  border-radius: 50%;
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  color: var(--dark-color);
  border: 1px solid #e0e0e0;

  &::after {
    content: "";
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px dashed rgba(0, 0, 0, 0.1);
    animation: rotate 20s linear infinite;
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const HowItWorksStepNumber = styled.span`
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--dark-color);
`;

const StepHeader = styled.div`
  background-color: #f1f1f1;
  padding: var(--spacing-lg);
  color: var(--dark-color);
  text-align: center;
  width: 100%;
  border-bottom: 1px solid #e0e0e0;
`;

const StepTitle = styled.h3`
  font-size: 2.25rem;
  margin-bottom: 0;
  color: var(--dark-color);
`;

const StepContent = styled.div`
  padding: var(--spacing-lg);
  text-align: center;
`;

const StepDescription = styled.p`
  color: var(--text-light);
  font-size: 1.5rem;
  max-width: 300px;
  margin: 0 auto;
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
`;

// Trending Skills section styles
const TrendingSection = styled.section`
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh; // Make section occupy full viewport height
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle at top right,
      rgba(106, 61, 232, 0.05),
      transparent 60%
    );
    pointer-events: none;
  }
`;

const TrendingTitle = styled.h2`
  font-size: var(--font-size-6xl);
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
  color: var(--dark-color);
  font-weight: 700;

  &:after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
  }

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);

    &:after {
      width: 60px;
      bottom: -10px;
    }
  }
`;

const TrendingSubtitle = styled.p`
  font-size: 1.5rem;
  color: var(--text-light);
  max-width: 700px;
  margin: 0 auto;
  margin-bottom: var(--spacing-2xl);
  margin-top: var(--spacing-xl);

  @media (max-width: 992px) {
    font-size: 1.3rem;
    max-width: 600px;
    margin-bottom: var(--spacing-xl);
  }

  @media (max-width: 768px) {
    font-size: 1.1rem;
    max-width: 500px;
  }

  @media (max-width: 576px) {
    font-size: 1rem;
    max-width: 100%;
    margin-bottom: var(--spacing-lg);
    margin-top: var(--spacing-md);
  }
`;

const SkillsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  width: calc(100% - 6rem);
  margin: 0 3rem;
  padding-bottom: var(--spacing-lg);
  justify-content: center;

  @media (max-width: 992px) {
    gap: var(--spacing-md);
  }

  @media (max-width: 576px) {
    width: calc(100% - 2rem);
    margin: 0 1rem;
    gap: var(--spacing-sm);
  }
`;

const SkillCard = styled.div`
  background-color: var(--light-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl);
  width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
  }

  @media (max-width: 992px) {
    width: 250px;
    padding: var(--spacing-lg);
  }

  @media (max-width: 576px) {
    width: 100%;
    max-width: 320px;
    padding: var(--spacing-md);
  }
`;

const SkillIcon = styled.div`
  width: 80px;
  height: 80px;
  background-color: #f1f1f1;
  border-radius: 50%;
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-color);
  font-size: 2rem;
  border: 1px solid #e0e0e0;
`;

const SkillName = styled.h3`
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--dark-color);
`;

const SkillStats = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--text-light);
`;

const SkillCount = styled.span`
  font-weight: bold;
  color: var(--primary-color);
  margin-right: 5px;
`;

const SkillDescription = styled.p`
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
`;

const LearnMoreButton = styled.a`
  display: inline-block;
  padding: 8px 20px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  color: var(--dark-color);
  font-size: var(--font-size-sm);
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  background-color: #f1f1f1;

  &:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
  }
`;

const ScrollIndicator = styled.div`
  position: relative;
  width: calc(100% - 6rem);
  height: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  margin: var(--spacing-xl) 3rem 0;
  overflow: hidden;
  border-radius: 2px;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background: linear-gradient(
      to right,
      var(--primary-color),
      var(--secondary-color)
    );
    border-radius: 2px;
  }
`;

const Home = () => {
  // FAQ state
  const [activeFAQTab, setActiveFAQTab] = useState("All questions");
  const [openFAQs, setOpenFAQs] = useState({});

  // Toggle FAQ item
  const toggleFAQ = (id) => {
    setOpenFAQs((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // FAQ data
  const faqData = [
    {
      id: 1,
      category: "General",
      question: "How to start skill exchange?",
      answer:
        "To start a skill exchange, create an account, list the skills you can offer and the ones you want to learn. Our matching algorithm will suggest potential exchange partners based on complementary needs.",
    },
    {
      id: 2,
      category: "Skill exchange",
      question: "How long does an exchange usually take?",
      answer:
        "The duration of skill exchanges varies depending on the complexity of the skills being taught. Most sessions last between 1-2 hours, and users typically meet 3-5 times to complete a basic skill exchange.",
    },
    {
      id: 3,
      category: "Technical",
      question: "What technology do I need for online exchanges?",
      answer:
        "For online exchanges, you need a stable internet connection, a device with a camera and microphone (smartphone, tablet, or computer), and our SkillSwap app or website. Some specific skills might require additional software.",
    },
    {
      id: 4,
      category: "General",
      question: "Is SkillSwap available worldwide?",
      answer:
        "Yes, SkillSwap is available globally. We have users from over 50 countries, and our platform supports multiple languages to facilitate international skill exchanges.",
    },
    {
      id: 5,
      category: "Skill exchange",
      question: "Can I exchange multiple skills at once?",
      answer:
        "Yes, you can participate in multiple skill exchanges simultaneously. However, we recommend focusing on 2-3 exchanges at a time to ensure quality learning and teaching experiences.",
    },
    {
      id: 6,
      category: "Technical",
      question: "How secure is my personal information?",
      answer:
        "We take data security seriously. All personal information is encrypted, and we never share your data with third parties without consent. We comply with global data protection regulations including GDPR.",
    },
  ];

  // Filter FAQs based on active tab
  const filteredFAQs =
    activeFAQTab === "All questions"
      ? faqData
      : faqData.filter((faq) => faq.category === activeFAQTab);

  return (
    <>
      <Hero
        bgImage={images.heroHome}
        title={
          <>
            Welcome to <span>SkillSwap</span>
          </>
        }
        subtitle="Exchange skills, learn together, and grow with a vibrant community of learners and mentors where knowledge flows both ways."
        actions={
          <>
            <Button as={Link} to="/register" size="large">
              Find Skills to Learn
            </Button>
            <Button as={Link} to="/register" size="large" variant="outline">
              Share Your Skills
            </Button>
          </>
        }
      />

      <HowItWorksSection>
        <HowItWorksTitle>How it works</HowItWorksTitle>

        <StepsContainer>
          <StepItem>
            <StepHeader>
              <StepIcon>
                <HowItWorksStepNumber>1</HowItWorksStepNumber>
              </StepIcon>
              <StepTitle>Register</StepTitle>
            </StepHeader>
            <StepContent>
              <StepDescription>
                Create an account and specify your skills and interests
              </StepDescription>
            </StepContent>
          </StepItem>

          <StepItem>
            <StepHeader>
              <StepIcon>
                <HowItWorksStepNumber>2</HowItWorksStepNumber>
              </StepIcon>
              <StepTitle>Choose a skill</StepTitle>
            </StepHeader>
            <StepContent>
              <StepDescription>
                Find a skill you're interested in or people to exchange with
              </StepDescription>
            </StepContent>
          </StepItem>

          <StepItem>
            <StepHeader>
              <StepIcon>
                <HowItWorksStepNumber>3</HowItWorksStepNumber>
              </StepIcon>
              <StepTitle>Start exchanging</StepTitle>
            </StepHeader>
            <StepContent>
              <StepDescription>
                Communicate and exchange knowledge in a convenient format
              </StepDescription>
            </StepContent>
          </StepItem>
        </StepsContainer>
      </HowItWorksSection>

      <TrendingSection>
        <TrendingTitle>Trending Skills</TrendingTitle>
        <TrendingSubtitle>
          Explore the most in-demand skills in our community
        </TrendingSubtitle>

        <SkillsContainer>
          <SkillCard>
            <SkillIcon bgColor="#4285f4">
              <FontAwesomeIcon icon={faBrain} />
            </SkillIcon>
            <SkillName>Data Science</SkillName>
            <SkillStats>
              <div>
                <SkillCount>3580</SkillCount>
              </div>
              <div>Beginner to advanced</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#34a853">
              <FontAwesomeIcon icon={faRobot} />
            </SkillIcon>
            <SkillName>AI</SkillName>
            <SkillStats>
              <div>
                <SkillCount>3240</SkillCount>
              </div>
              <div>Machine learning & more</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#6d4c41">
              <FontAwesomeIcon icon={faLink} />
            </SkillIcon>
            <SkillName>Blockchain & Web3</SkillName>
            <SkillStats>
              <div>
                <SkillCount>2850</SkillCount>
              </div>
              <div>Crypto & smart contracts</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#0097a7">
              <FontAwesomeIcon icon={faServer} />
            </SkillIcon>
            <SkillName>DevOps</SkillName>
            <SkillStats>
              <div>
                <SkillCount>2430</SkillCount>
              </div>
              <div>CI/CD & cloud platforms</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#d32f2f">
              <FontAwesomeIcon icon={faShieldAlt} />
            </SkillIcon>
            <SkillName>Cybersecurity</SkillName>
            <SkillStats>
              <div>
                <SkillCount>2180</SkillCount>
              </div>
              <div>Ethical hacking & defense</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#7b1fa2">
              <FontAwesomeIcon icon={faTasks} />
            </SkillIcon>
            <SkillName>Product Management</SkillName>
            <SkillStats>
              <div>
                <SkillCount>1950</SkillCount>
              </div>
              <div>Agile & product strategy</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#0288d1">
              <FontAwesomeIcon icon={faComments} />
            </SkillIcon>
            <SkillName>Effective Communication</SkillName>
            <SkillStats>
              <div>
                <SkillCount>1830</SkillCount>
              </div>
              <div>Public speaking & more</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#00796b">
              <FontAwesomeIcon icon={faPen} />
            </SkillIcon>
            <SkillName>Content Creation</SkillName>
            <SkillStats>
              <div>
                <SkillCount>1720</SkillCount>
              </div>
              <div>Writing & storytelling</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#e64a19">
              <FontAwesomeIcon icon={faVideo} />
            </SkillIcon>
            <SkillName>Video Editing</SkillName>
            <SkillStats>
              <div>
                <SkillCount>1650</SkillCount>
              </div>
              <div>Professional techniques</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>

          <SkillCard>
            <SkillIcon bgColor="#fbc02d">
              <FontAwesomeIcon icon={faLightbulb} />
            </SkillIcon>
            <SkillName>Prompt Engineering</SkillName>
            <SkillStats>
              <div>
                <SkillCount>1580</SkillCount>
              </div>
              <div>AI prompt mastery</div>
            </SkillStats>
            <LearnMoreButton as={Link} to="/discover">
              Explore Skill
            </LearnMoreButton>
          </SkillCard>
        </SkillsContainer>

        <ScrollIndicator />
      </TrendingSection>

      <Section
        title="Our Services"
        subtitle="Discover the various ways SkillSwap can help you learn new skills or share your expertise with others."
      >
        <ServicesGrid>
          <ServiceCard hoverable>
            <ServiceIcon>
              <FontAwesomeIcon icon={faExchangeAlt} />
            </ServiceIcon>
            <h3>Skill Sharing</h3>
            <p>
              Connect with others to exchange knowledge, teach what you know and
              learn something new in return.
            </p>
          </ServiceCard>

          <ServiceCard hoverable>
            <ServiceIcon>
              <FontAwesomeIcon icon={faUsers} />
            </ServiceIcon>
            <h3>Community</h3>
            <p>
              Join a vibrant network of learners and experts who grow together
              through support and collaboration.
            </p>
          </ServiceCard>

          <ServiceCard hoverable>
            <ServiceIcon>
              <FontAwesomeIcon icon={faPeopleArrows} />
            </ServiceIcon>
            <h3>Peer Learning</h3>
            <p>
              Learn alongside peers who share your interests, making the journey
              more interactive and fun.
            </p>
          </ServiceCard>

          <ServiceCard hoverable>
            <ServiceIcon>
              <FontAwesomeIcon icon={faUserFriends} />
            </ServiceIcon>
            <h3>Growth Together</h3>
            <p>
              Support each other's learning goals and grow together through
              meaningful skill exchanges.
            </p>
          </ServiceCard>

          <ServiceCard hoverable>
            <ServiceIcon>
              <FontAwesomeIcon icon={faBalanceScale} />
            </ServiceIcon>
            <h3>Swap & Learn</h3>
            <p>
              Trade skills with others, no money involved, just mutual learning
              and shared growth.
            </p>
          </ServiceCard>

          <ServiceCard hoverable>
            <ServiceIcon>
              <FontAwesomeIcon icon={faHandsHelping} />
            </ServiceIcon>
            <h3>Mutual Support</h3>
            <p>
              Help others succeed while reaching your own goals through shared
              learning and encouragement.
            </p>
          </ServiceCard>
        </ServicesGrid>
      </Section>

      <Section bgColor="var(--background-color)">
        <div style={{ textAlign: "center", marginBottom: "var(--spacing-xl)" }}>
          <WhyChooseTitle>Why Choose SkillSwap?</WhyChooseTitle>
        </div>
        <AboutContainer>
          <AboutImage src={images.aboutImage} alt="About SkillSwap" />
          <AboutContent>
            <p>
              SkillSwap is a platform dedicated to connecting people who want to
              learn with those who want to teach. We believe that everyone has
              valuable skills to share, and our mission is to make skill sharing
              accessible, enjoyable, and rewarding for everyone.
            </p>

            <FeatureList>
              <FeatureItem>
                <FontAwesomeIcon icon={faUsers} />
                <span>
                  Connect with a diverse community of experts and learners
                </span>
              </FeatureItem>
              <FeatureItem>
                <FontAwesomeIcon icon={faGraduationCap} />
                <span>
                  Learn new skills at your own pace with personalized guidance
                </span>
              </FeatureItem>
              <FeatureItem>
                <FontAwesomeIcon icon={faHandshake} />
                <span>
                  Share your expertise and earn while helping others grow
                </span>
              </FeatureItem>
              <FeatureItem>
                <FontAwesomeIcon icon={faLaptopCode} />
                <span>
                  Access a wide range of courses and resources in various fields
                </span>
              </FeatureItem>
            </FeatureList>

            <Button
              as={Link}
              to="/about"
              style={{
                backgroundColor: "#f1f1f1",
                color: "var(--dark-color)",
                border: "1px solid #e0e0e0",
                padding: "0.6rem 1.2rem",
                borderRadius: "50px",
                fontWeight: "bold",
                transition: "all 0.3s ease",
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = "#e0e0e0";
                e.currentTarget.style.transform = "translateY(-2px)";
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = "#f1f1f1";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              Discover Our Community{" "}
              <FontAwesomeIcon
                icon={faArrowRight}
                style={{ marginLeft: "0.5rem" }}
              />
            </Button>
          </AboutContent>
        </AboutContainer>
      </Section>

      <FeedbackSection>
        <FeedbackTitle>Community Feedback</FeedbackTitle>
        <TestimonialsContainer>
          <TestimonialCard>
            <TestimonialText>
              "SkillSwap changed my life! I found new friends and learned to
              draw."
            </TestimonialText>
            <TestimonialAuthor>Haresh-Umerkot</TestimonialAuthor>
          </TestimonialCard>

          <TestimonialCard>
            <TestimonialText>
              "I never thought I would learn a programming language so quickly
              and with such pleasure!"
            </TestimonialText>
            <TestimonialAuthor>Sohail-Mithi</TestimonialAuthor>
          </TestimonialCard>

          <TestimonialCard>
            <TestimonialText>
              "The best platform for those who want to expand their horizons and
              share knowledge."
            </TestimonialText>
            <TestimonialAuthor>Khushal-Tharparkar</TestimonialAuthor>
          </TestimonialCard>
        </TestimonialsContainer>
      </FeedbackSection>

      <Section
        title="Our Impact"
        subtitle="SkillSwap is making a difference in how people learn and share knowledge around the world."
      >
        <StatsContainer>
          <StatCard>
            <StatNumber>10,000+</StatNumber>
            <StatTitle>Active Users</StatTitle>
          </StatCard>

          <StatCard>
            <StatNumber>500+</StatNumber>
            <StatTitle>Expert Mentors</StatTitle>
          </StatCard>

          <StatCard>
            <StatNumber>1,200+</StatNumber>
            <StatTitle>Skills Shared</StatTitle>
          </StatCard>

          <StatCard>
            <StatNumber>50+</StatNumber>
            <StatTitle>Countries</StatTitle>
          </StatCard>
        </StatsContainer>
      </Section>

      <FAQSection>
        <FAQTitle>Frequently Asked Questions</FAQTitle>
        <FAQSubtitle>
          Find answers to the most popular questions about our platform
        </FAQSubtitle>

        <FAQTabsContainer>
          <FAQTab
            active={activeFAQTab === "All questions"}
            onClick={() => setActiveFAQTab("All questions")}
          >
            All questions
          </FAQTab>
          <FAQTab
            active={activeFAQTab === "General"}
            onClick={() => setActiveFAQTab("General")}
          >
            General
          </FAQTab>
          <FAQTab
            active={activeFAQTab === "Skill exchange"}
            onClick={() => setActiveFAQTab("Skill exchange")}
          >
            Skill exchange
          </FAQTab>
          <FAQTab
            active={activeFAQTab === "Technical"}
            onClick={() => setActiveFAQTab("Technical")}
          >
            Technical
          </FAQTab>
        </FAQTabsContainer>

        <FAQContainer>
          {filteredFAQs.map((faq) => (
            <FAQItem key={faq.id} onClick={() => toggleFAQ(faq.id)}>
              <FAQHeader isOpen={openFAQs[faq.id]}>
                <div>
                  <FAQCategory category={faq.category}>
                    {faq.category}
                  </FAQCategory>
                  <FAQQuestion>{faq.question}</FAQQuestion>
                </div>
                <FAQToggle isOpen={openFAQs[faq.id]}>
                  {openFAQs[faq.id] ? "-" : "+"}
                </FAQToggle>
              </FAQHeader>
              <FAQContent isOpen={openFAQs[faq.id]}>
                <FAQAnswer>{faq.answer}</FAQAnswer>
              </FAQContent>
            </FAQItem>
          ))}

          <FAQSupportContainer>
            <FAQSupportText>
              Didn't find an answer to your question?
            </FAQSupportText>
            <FAQSupportButton as={Link} to="/contact">
              Ask the Community
            </FAQSupportButton>
          </FAQSupportContainer>
        </FAQContainer>
      </FAQSection>

      <WhereToStartSection>
        <WhereToStartTitle>Where to Start</WhereToStartTitle>
        <StepsGrid>
          <StepCard>
            <WhereToStartStepNumber>1</WhereToStartStepNumber>
            <StepText>Register and create a profile</StepText>
          </StepCard>

          <TipCard>
            <TipText>
              Tip: Start small! Choose one skill to learn and one to teach.
            </TipText>
          </TipCard>

          <StepCard>
            <WhereToStartStepNumber>2</WhereToStartStepNumber>
            <StepText>Specify the skills you have</StepText>
          </StepCard>

          <StepCard>
            <WhereToStartStepNumber>3</WhereToStartStepNumber>
            <StepText>Choose skills you want to learn</StepText>
          </StepCard>

          <StepCard>
            <WhereToStartStepNumber>4</WhereToStartStepNumber>
            <StepText>Find a suitable exchange partner</StepText>
          </StepCard>

          <StepCard>
            <WhereToStartStepNumber>5</WhereToStartStepNumber>
            <StepText>Start exchanging skills and knowledge</StepText>
          </StepCard>
        </StepsGrid>
      </WhereToStartSection>

      <ValuesSection>
        <ValuesTitle>Our Values</ValuesTitle>
        <ValuesGrid>
          <ValueCard>
            <ValueIcon bgColor="#4285f4">
              <FontAwesomeIcon icon={faUniversalAccess} />
            </ValueIcon>
            <ValueTitle>Accessibility</ValueTitle>
            <ValueDescription>
              Learning is accessible to everyone, regardless of location
            </ValueDescription>
          </ValueCard>

          <ValueCard>
            <ValueIcon bgColor="#34a853">
              <FontAwesomeIcon icon={faExchangeAlt} />
            </ValueIcon>
            <ValueTitle>Exchange</ValueTitle>
            <ValueDescription>
              Mutual exchange of knowledge and experience between participants
            </ValueDescription>
          </ValueCard>

          <ValueCard>
            <ValueIcon bgColor="#ea4335">
              <FontAwesomeIcon icon={faChartLine} />
            </ValueIcon>
            <ValueTitle>Growth</ValueTitle>
            <ValueDescription>
              Continuous development and improvement of skills
            </ValueDescription>
          </ValueCard>

          <ValueCard>
            <ValueIcon bgColor="#fbbc05">
              <FontAwesomeIcon icon={faGlobe} />
            </ValueIcon>
            <ValueTitle>Community</ValueTitle>
            <ValueDescription>
              Creating a global network of like-minded people
            </ValueDescription>
          </ValueCard>
        </ValuesGrid>
      </ValuesSection>

      <JoinNowSection>
        <JoinNowTitle>Join Now</JoinNowTitle>
        <JoinNowText>Do you want to start your journey?</JoinNowText>
        <JoinNowButton as={Link} to="/register">
          Start Exchanging Skills
        </JoinNowButton>
      </JoinNowSection>
    </>
  );
};

export default Home;
