const mysql = require('mysql2/promise');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('Testing database connection...');
  console.log('Host:', process.env.DB_HOST);
  console.log('User:', process.env.DB_USER);
  console.log('Database:', process.env.DB_NAME);
  console.log('Port:', process.env.DB_PORT || 3306);

  try {
    // First, try to connect without specifying a database
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      port: process.env.DB_PORT || 3306
    });

    console.log('✅ Successfully connected to MySQL server');

    // Check if database exists
    const [databases] = await connection.execute('SHOW DATABASES');
    const dbExists = databases.some(db => db.Database === process.env.DB_NAME);

    if (!dbExists) {
      console.log(`❌ Database '${process.env.DB_NAME}' does not exist`);
      console.log(`Creating database '${process.env.DB_NAME}'...`);
      
      await connection.execute(`CREATE DATABASE \`${process.env.DB_NAME}\``);
      console.log(`✅ Database '${process.env.DB_NAME}' created successfully`);
    } else {
      console.log(`✅ Database '${process.env.DB_NAME}' exists`);
    }

    // Test connection to the specific database
    await connection.changeUser({ database: process.env.DB_NAME });
    console.log(`✅ Successfully connected to database '${process.env.DB_NAME}'`);

    await connection.end();
    console.log('✅ Database connection test completed successfully');
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n🔧 Troubleshooting steps:');
      console.log('1. Check if MySQL is running');
      console.log('2. Verify the username and password in .env file');
      console.log('3. Make sure the user has proper permissions');
      console.log('4. Try connecting with MySQL client: mysql -u root -p');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n🔧 Troubleshooting steps:');
      console.log('1. Check if MySQL server is running');
      console.log('2. Verify the host and port in .env file');
      console.log('3. Check if MySQL is listening on the specified port');
    }
  }
}

testDatabaseConnection();
