const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// Test data
const testUser = {
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'Password123',
  confirmPassword: 'Password123'
};

const testSkill = {
  name: 'JavaScript Programming',
  description: 'Modern JavaScript development including ES6+ features',
  category: 'technology',
  level: 'intermediate'
};

async function testAPI() {
  console.log('🚀 Starting API Tests...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data.message);
    console.log('');

    // Test 2: User Registration
    console.log('2. Testing User Registration...');
    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
      console.log('✅ User Registration:', registerResponse.data.message);
      console.log('User ID:', registerResponse.data.user.id);
      console.log('Token received:', !!registerResponse.data.token);
      
      const token = registerResponse.data.token;
      console.log('');

      // Test 3: User Login
      console.log('3. Testing User Login...');
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      });
      console.log('✅ User Login:', loginResponse.data.message);
      console.log('');

      // Test 4: Get User Profile
      console.log('4. Testing Get User Profile...');
      const profileResponse = await axios.get(`${BASE_URL}/auth/me`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Get Profile:', profileResponse.data.message);
      console.log('User Name:', `${profileResponse.data.user.firstName} ${profileResponse.data.user.lastName}`);
      console.log('');

      // Test 5: Create Skill
      console.log('5. Testing Create Skill...');
      const skillResponse = await axios.post(`${BASE_URL}/skills`, testSkill, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Create Skill:', skillResponse.data.message);
      console.log('Skill ID:', skillResponse.data.skill.id);
      console.log('');

      // Test 6: Get Skills
      console.log('6. Testing Get Skills...');
      const skillsResponse = await axios.get(`${BASE_URL}/skills`);
      console.log('✅ Get Skills:', skillsResponse.data.message);
      console.log('Total Skills:', skillsResponse.data.skills.length);
      console.log('');

      // Test 7: Add Skill to User Profile
      console.log('7. Testing Add Skill to User Profile...');
      const userSkillResponse = await axios.post(`${BASE_URL}/skills/user-skills`, {
        skillId: skillResponse.data.skill.id,
        proficiencyLevel: 'intermediate',
        yearsOfExperience: 3,
        isTeaching: true,
        isLearning: false,
        description: 'I have 3 years of experience with JavaScript and can teach others'
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Add User Skill:', userSkillResponse.data.message);
      console.log('');

      // Test 8: Get User Skills
      console.log('8. Testing Get User Skills...');
      const userSkillsResponse = await axios.get(`${BASE_URL}/skills/user-skills/me`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Get User Skills:', userSkillsResponse.data.message);
      console.log('User Skills Count:', userSkillsResponse.data.userSkills.length);
      console.log('');

      // Test 9: Update User Profile
      console.log('9. Testing Update User Profile...');
      const updateProfileResponse = await axios.put(`${BASE_URL}/users/profile`, {
        bio: 'I am a passionate developer who loves to learn and teach new technologies.',
        location: 'New York, USA',
        phone: '+1234567890'
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Update Profile:', updateProfileResponse.data.message);
      console.log('');

      console.log('🎉 All API tests completed successfully!');
      console.log('\n📋 Summary:');
      console.log('- User registration and authentication: ✅');
      console.log('- Profile management: ✅');
      console.log('- Skill management: ✅');
      console.log('- User skill management: ✅');
      console.log('\n🔗 You can now test the API manually at: http://localhost:3001/api/health');

    } catch (error) {
      if (error.response?.status === 409 && error.response?.data?.error === 'EMAIL_EXISTS') {
        console.log('ℹ️  User already exists, testing login instead...');
        
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: testUser.email,
          password: testUser.password
        });
        console.log('✅ User Login:', loginResponse.data.message);
        console.log('🎉 API is working! User already exists from previous test.');
      } else {
        throw error;
      }
    }

  } catch (error) {
    console.error('❌ API Test Failed:');
    if (error.code === 'ECONNREFUSED') {
      console.error('Server is not running. Please start the server with: npm run dev');
    } else if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Check if axios is available
if (typeof axios === 'undefined') {
  console.log('Installing axios for API testing...');
  const { exec } = require('child_process');
  exec('npm install axios', (error) => {
    if (error) {
      console.error('Failed to install axios. Please run: npm install axios');
      return;
    }
    console.log('Axios installed successfully!');
    testAPI();
  });
} else {
  testAPI();
}
