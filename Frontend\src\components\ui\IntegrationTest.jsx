import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../context/AuthContext';
import { healthCheck, skillsAPI, usersAPI } from '../../services/api';

const TestContainer = styled.div`
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  font-size: 0.9rem;
`;

const TestTitle = styled.h4`
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
`;

const TestItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
`;

const TestLabel = styled.span`
  color: #666;
`;

const TestStatus = styled.span`
  color: ${props => {
    switch(props.status) {
      case 'success': return '#4caf50';
      case 'error': return '#f44336';
      case 'loading': return '#ff9800';
      default: return '#999';
    }
  }};
  font-weight: 500;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #999;
  
  &:hover {
    color: #333;
  }
`;

const IntegrationTest = () => {
  const { currentUser } = useAuth();
  const [visible, setVisible] = useState(true);
  const [tests, setTests] = useState({
    health: { status: 'loading', message: 'Testing...' },
    auth: { status: 'loading', message: 'Testing...' },
    skills: { status: 'loading', message: 'Testing...' },
    profile: { status: 'loading', message: 'Testing...' }
  });

  useEffect(() => {
    runTests();
  }, [currentUser]);

  const updateTest = (testName, status, message) => {
    setTests(prev => ({
      ...prev,
      [testName]: { status, message }
    }));
  };

  const runTests = async () => {
    // Test 1: Health Check
    try {
      await healthCheck();
      updateTest('health', 'success', 'Connected');
    } catch (error) {
      updateTest('health', 'error', 'Failed');
    }

    // Test 2: Authentication
    if (currentUser) {
      updateTest('auth', 'success', 'Logged in');
      
      // Test 3: Skills API
      try {
        await skillsAPI.getAllSkills({ limit: 1 });
        updateTest('skills', 'success', 'Working');
      } catch (error) {
        updateTest('skills', 'error', 'Failed');
      }

      // Test 4: Profile API
      try {
        await usersAPI.getProfile();
        updateTest('profile', 'success', 'Working');
      } catch (error) {
        updateTest('profile', 'error', 'Failed');
      }
    } else {
      updateTest('auth', 'error', 'Not logged in');
      updateTest('skills', 'error', 'Auth required');
      updateTest('profile', 'error', 'Auth required');
    }
  };

  if (!visible) return null;

  return (
    <TestContainer>
      <CloseButton onClick={() => setVisible(false)}>×</CloseButton>
      <TestTitle>API Integration Test</TestTitle>
      
      <TestItem>
        <TestLabel>Backend Health:</TestLabel>
        <TestStatus status={tests.health.status}>
          {tests.health.message}
        </TestStatus>
      </TestItem>

      <TestItem>
        <TestLabel>Authentication:</TestLabel>
        <TestStatus status={tests.auth.status}>
          {tests.auth.message}
        </TestStatus>
      </TestItem>

      <TestItem>
        <TestLabel>Skills API:</TestLabel>
        <TestStatus status={tests.skills.status}>
          {tests.skills.message}
        </TestStatus>
      </TestItem>

      <TestItem>
        <TestLabel>Profile API:</TestLabel>
        <TestStatus status={tests.profile.status}>
          {tests.profile.message}
        </TestStatus>
      </TestItem>

      {currentUser && (
        <TestItem>
          <TestLabel>User:</TestLabel>
          <TestStatus status="success">
            {currentUser.firstName} {currentUser.lastName}
          </TestStatus>
        </TestItem>
      )}
    </TestContainer>
  );
};

export default IntegrationTest;
