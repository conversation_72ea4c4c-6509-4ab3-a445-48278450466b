import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faTimesCircle,
  faBan,
  faUndo,
  faFlag,
  faExclamationTriangle,
  faSearch,
  faFilter,
  faEye,
  faComment
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const Container = styled.div``;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FilterBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
`;

const SearchInput = styled.div`
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  flex: 1;
  max-width: 400px;
  border: 1px solid var(--border-color);

  input {
    border: none;
    background: transparent;
    padding: 0.5rem;
    width: 100%;
    outline: none;
    color: var(--text-color);
  }
`;

const FilterDropdown = styled.select`
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: white;
  color: var(--text-color);
  cursor: pointer;
`;

const UserTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
`;

const TableHead = styled.thead`
  background-color: #f8f9fa;
  border-bottom: 2px solid var(--border-color);

  th {
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }
  }

  td {
    padding: 1rem;
    color: var(--text-color);
  }
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
`;

const UserImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
`;

const UserName = styled.div`
  font-weight: 600;
  color: var(--dark-color);
`;

const UserEmail = styled.div`
  font-size: 0.85rem;
  color: var(--text-light);
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  background-color: ${props => {
    switch (props.status) {
      case 'active': return '#e6f7ed';
      case 'pending': return '#fff8e6';
      case 'suspended': return '#ffebee';
      case 'blocked': return '#fce8e8';
      default: return '#f1f1f1';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'active': return '#2e7d32';
      case 'pending': return '#f57c00';
      case 'suspended': return '#c62828';
      case 'blocked': return '#d32f2f';
      default: return 'var(--text-color)';
    }
  }};
`;

const ActionButton = styled.button`
  background-color: transparent;
  color: ${props => {
    switch (props.action) {
      case 'approve': return '#2e7d32';
      case 'suspend': return '#f57c00';
      case 'block': return '#d32f2f';
      case 'view': return 'var(--primary-color)';
      default: return 'var(--text-color)';
    }
  }};
  border: 1px solid ${props => {
    switch (props.action) {
      case 'approve': return '#2e7d32';
      case 'suspend': return '#f57c00';
      case 'block': return '#d32f2f';
      case 'view': return 'var(--primary-color)';
      default: return 'var(--border-color)';
    }
  }};
  border-radius: 4px;
  padding: 0.5rem;
  margin-right: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => {
      switch (props.action) {
        case 'approve': return '#e6f7ed';
        case 'suspend': return '#fff8e6';
        case 'block': return '#ffebee';
        case 'view': return 'rgba(var(--primary-rgb), 0.1)';
        default: return '#f1f1f1';
      }
    }};
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const TabsContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
`;

const Tab = styled.button`
  padding: 0.75rem 1rem;
  background-color: ${props => props.active ? 'white' : 'transparent'};
  color: ${props => props.active ? 'var(--primary-color)' : 'var(--text-color)'};
  border: none;
  border-bottom: ${props => props.active ? '2px solid var(--primary-color)' : '2px solid transparent'};
  font-weight: ${props => props.active ? 'bold' : 'normal'};
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    color: var(--primary-color);
  }
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
`;

const PageButton = styled.button`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${props => props.active ? 'var(--primary-color)' : 'white'};
  color: ${props => props.active ? 'white' : 'var(--text-color)'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : 'var(--border-color)'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : '#f8f9fa'};
  }
`;

const ReportModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 2rem;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
`;

const ModalTitle = styled.h3`
  font-size: 1.5rem;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CloseButton = styled.button`
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  
  &:hover {
    color: var(--dark-color);
  }
`;

const ReportDetails = styled.div`
  margin-bottom: 1.5rem;
`;

const ReportItem = styled.div`
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 1rem;
`;

const ReportHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
`;

const ReportType = styled.div`
  font-weight: 600;
  color: var(--dark-color);
`;

const ReportDate = styled.div`
  font-size: 0.85rem;
  color: var(--text-light);
`;

const ReportContent = styled.p`
  margin-bottom: 1rem;
  color: var(--text-color);
`;

const ResponseArea = styled.textarea`
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 1rem;
  font-family: inherit;
`;

const ModalActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

// Sample data for users
const sampleUsers = [
  {
    id: 1,
    name: 'Ayesha Malik',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    status: 'active',
    joinDate: '2023-05-15',
    reports: 0
  },
  {
    id: 2,
    name: 'Ahmad Khan',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    status: 'active',
    joinDate: '2023-04-22',
    reports: 0
  },
  {
    id: 3,
    name: 'Bilal Ahmed',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
    status: 'pending',
    joinDate: '2023-06-01',
    reports: 0
  },
  {
    id: 4,
    name: 'Sara Ali',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
    status: 'suspended',
    joinDate: '2023-03-10',
    reports: 2
  },
  {
    id: 5,
    name: 'Usman Farooq',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    status: 'active',
    joinDate: '2023-02-18',
    reports: 0
  },
  {
    id: 6,
    name: 'Zainab Qureshi',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    status: 'blocked',
    joinDate: '2023-01-05',
    reports: 5
  }
];

// Sample data for reports
const sampleReports = [
  {
    id: 1,
    userId: 4,
    reportedBy: 'Ayesha Malik',
    type: 'Inappropriate Behavior',
    content: 'This user has been sending spam messages and inappropriate content during skill exchange sessions.',
    date: '2023-05-20',
    status: 'pending'
  },
  {
    id: 2,
    userId: 4,
    reportedBy: 'Ahmad Khan',
    type: 'Misleading Information',
    content: 'The user claims to be an expert in Python programming but doesn\'t seem to have basic knowledge during our session.',
    date: '2023-05-18',
    status: 'pending'
  },
  {
    id: 3,
    userId: 6,
    reportedBy: 'Bilal Ahmed',
    type: 'Harassment',
    content: 'This user has been sending threatening messages after I declined to continue our skill exchange sessions.',
    date: '2023-04-25',
    status: 'resolved'
  },
  {
    id: 4,
    userId: 6,
    reportedBy: 'Usman Farooq',
    type: 'Fake Profile',
    content: 'I believe this is a fake profile. The user claims to be from different cities in different conversations and the profile picture appears to be stock photography.',
    date: '2023-04-22',
    status: 'resolved'
  },
  {
    id: 5,
    userId: 6,
    reportedBy: 'Sara Ali',
    type: 'Inappropriate Content',
    content: 'The user shared inappropriate links during our skill exchange session that led to suspicious websites.',
    date: '2023-04-20',
    status: 'resolved'
  }
];

const UserManagement = () => {
  const [users, setUsers] = useState(sampleUsers);
  const [reports, setReports] = useState(sampleReports);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [responseText, setResponseText] = useState('');

  const handleStatusChange = (userId, newStatus) => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, status: newStatus } : user
    ));
  };

  const handleViewReports = (user) => {
    setSelectedUser(user);
    setShowReportModal(true);
  };

  const handleCloseModal = () => {
    setShowReportModal(false);
    setSelectedUser(null);
    setResponseText('');
  };

  const handleResolveReport = (reportId) => {
    setReports(reports.map(report => 
      report.id === reportId ? { ...report, status: 'resolved' } : report
    ));
  };

  const handleSendResponse = () => {
    // In a real app, this would send the response to the user
    alert(`Response sent to ${selectedUser.name}`);
    handleCloseModal();
  };

  const filteredUsers = users.filter(user => {
    // Filter by search query
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         user.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Filter by status
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    // Filter by tab
    const matchesTab = activeTab === 'all' || 
                      (activeTab === 'pending' && user.status === 'pending') ||
                      (activeTab === 'reported' && user.reports > 0);
    
    return matchesSearch && matchesStatus && matchesTab;
  });

  const userReports = selectedUser ? 
    reports.filter(report => report.userId === selectedUser.id) : [];

  return (
    <Container>
      <SectionTitle>
        <FontAwesomeIcon icon={faUsers} />
        User Management
      </SectionTitle>

      <TabsContainer>
        <Tab 
          active={activeTab === 'all'} 
          onClick={() => setActiveTab('all')}
        >
          All Users
        </Tab>
        <Tab 
          active={activeTab === 'pending'} 
          onClick={() => setActiveTab('pending')}
        >
          Pending Approval
        </Tab>
        <Tab 
          active={activeTab === 'reported'} 
          onClick={() => setActiveTab('reported')}
        >
          <FontAwesomeIcon icon={faFlag} />
          Reported Users
        </Tab>
      </TabsContainer>

      <FilterBar>
        <SearchInput>
          <FontAwesomeIcon icon={faSearch} style={{ color: 'var(--text-light)' }} />
          <input 
            type="text" 
            placeholder="Search users..." 
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </SearchInput>

        <FilterDropdown 
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="all">All Statuses</option>
          <option value="active">Active</option>
          <option value="pending">Pending</option>
          <option value="suspended">Suspended</option>
          <option value="blocked">Blocked</option>
        </FilterDropdown>
      </FilterBar>

      <UserTable>
        <TableHead>
          <tr>
            <th>User</th>
            <th>Status</th>
            <th>Join Date</th>
            <th>Reports</th>
            <th>Actions</th>
          </tr>
        </TableHead>
        <TableBody>
          {filteredUsers.map(user => (
            <tr key={user.id}>
              <td>
                <UserInfo>
                  <UserAvatar>
                    <UserImage src={user.avatar} alt={user.name} />
                  </UserAvatar>
                  <div>
                    <UserName>{user.name}</UserName>
                    <UserEmail>{user.email}</UserEmail>
                  </div>
                </UserInfo>
              </td>
              <td>
                <StatusBadge status={user.status}>
                  {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                </StatusBadge>
              </td>
              <td>{user.joinDate}</td>
              <td>
                {user.reports > 0 ? (
                  <span style={{ color: '#d32f2f', fontWeight: 'bold' }}>
                    {user.reports}
                  </span>
                ) : (
                  <span>0</span>
                )}
              </td>
              <td>
                <ActionButtons>
                  {user.status === 'pending' && (
                    <ActionButton 
                      action="approve"
                      title="Approve User"
                      onClick={() => handleStatusChange(user.id, 'active')}
                    >
                      <FontAwesomeIcon icon={faCheckCircle} />
                    </ActionButton>
                  )}
                  
                  {user.status !== 'blocked' && user.status !== 'suspended' && (
                    <ActionButton 
                      action="suspend"
                      title="Suspend User"
                      onClick={() => handleStatusChange(user.id, 'suspended')}
                    >
                      <FontAwesomeIcon icon={faTimesCircle} />
                    </ActionButton>
                  )}
                  
                  {user.status !== 'blocked' && (
                    <ActionButton 
                      action="block"
                      title="Block User"
                      onClick={() => handleStatusChange(user.id, 'blocked')}
                    >
                      <FontAwesomeIcon icon={faBan} />
                    </ActionButton>
                  )}
                  
                  {(user.status === 'blocked' || user.status === 'suspended') && (
                    <ActionButton 
                      action="approve"
                      title="Restore User"
                      onClick={() => handleStatusChange(user.id, 'active')}
                    >
                      <FontAwesomeIcon icon={faUndo} />
                    </ActionButton>
                  )}
                  
                  {user.reports > 0 && (
                    <ActionButton 
                      action="view"
                      title="View Reports"
                      onClick={() => handleViewReports(user)}
                    >
                      <FontAwesomeIcon icon={faFlag} />
                    </ActionButton>
                  )}
                </ActionButtons>
              </td>
            </tr>
          ))}
        </TableBody>
      </UserTable>

      <Pagination>
        <PageButton active={true}>1</PageButton>
        <PageButton>2</PageButton>
        <PageButton>3</PageButton>
        <PageButton>...</PageButton>
        <PageButton>10</PageButton>
      </Pagination>

      {showReportModal && selectedUser && (
        <ReportModal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                <FontAwesomeIcon icon={faFlag} style={{ color: '#d32f2f' }} />
                Reports for {selectedUser.name}
              </ModalTitle>
              <CloseButton onClick={handleCloseModal}>&times;</CloseButton>
            </ModalHeader>
            
            <ReportDetails>
              {userReports.length > 0 ? (
                userReports.map(report => (
                  <ReportItem key={report.id}>
                    <ReportHeader>
                      <ReportType>
                        <FontAwesomeIcon icon={faExclamationTriangle} style={{ color: '#f57c00', marginRight: '0.5rem' }} />
                        {report.type}
                      </ReportType>
                      <ReportDate>{report.date}</ReportDate>
                    </ReportHeader>
                    <div style={{ fontSize: '0.9rem', color: 'var(--text-light)', marginBottom: '0.5rem' }}>
                      Reported by: {report.reportedBy}
                    </div>
                    <ReportContent>{report.content}</ReportContent>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <StatusBadge status={report.status === 'resolved' ? 'active' : 'pending'}>
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </StatusBadge>
                      {report.status !== 'resolved' && (
                        <ActionButton 
                          action="approve"
                          onClick={() => handleResolveReport(report.id)}
                        >
                          <FontAwesomeIcon icon={faCheckCircle} /> Mark as Resolved
                        </ActionButton>
                      )}
                    </div>
                  </ReportItem>
                ))
              ) : (
                <p>No reports found for this user.</p>
              )}
            </ReportDetails>

            <div>
              <h4 style={{ marginBottom: '0.5rem' }}>Send Response to User</h4>
              <ResponseArea 
                placeholder="Write your response here..."
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
              />
            </div>

            <ModalActions>
              <button 
                style={{
                  padding: '0.75rem 1.5rem',
                  backgroundColor: 'transparent',
                  color: 'var(--text-color)',
                  border: '1px solid var(--border-color)',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
                onClick={handleCloseModal}
              >
                Cancel
              </button>
              <button 
                style={{
                  padding: '0.75rem 1.5rem',
                  backgroundColor: 'var(--primary-color)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
                onClick={handleSendResponse}
                disabled={!responseText.trim()}
              >
                <FontAwesomeIcon icon={faComment} style={{ marginRight: '0.5rem' }} />
                Send Response
              </button>
            </ModalActions>
          </ModalContent>
        </ReportModal>
      )}
    </Container>
  );
};

export default UserManagement;
