const express = require('express');
const { Op } = require('sequelize');
const { Skill, UserSkill, User } = require('../models');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { validateSkill, validateUserSkill } = require('../middleware/validation');

const router = express.Router();

// Get all skills
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 20, search, category, level } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { isActive: true };
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    if (category) {
      whereClause.category = category;
    }

    if (level) {
      whereClause.level = level;
    }

    const skills = await Skill.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']]
    });

    res.json({
      message: 'Skills retrieved successfully',
      skills: skills.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(skills.count / limit),
        totalSkills: skills.count,
        hasNext: page * limit < skills.count,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Get skills error:', error);
    res.status(500).json({
      message: 'Failed to retrieve skills',
      error: 'SKILLS_ERROR'
    });
  }
});

// Get skill categories
router.get('/categories', (req, res) => {
  const categories = [
    'technology',
    'language',
    'music',
    'art',
    'sports',
    'cooking',
    'business',
    'academic',
    'crafts',
    'other'
  ];

  res.json({
    message: 'Skill categories retrieved successfully',
    categories
  });
});

// Create new skill
router.post('/', authenticateToken, validateSkill, async (req, res) => {
  try {
    const { name, description, category, level } = req.body;

    // Check if skill already exists
    const existingSkill = await Skill.findOne({
      where: {
        name: { [Op.iLike]: name },
        category
      }
    });

    if (existingSkill) {
      return res.status(409).json({
        message: 'Skill with this name already exists in this category',
        error: 'SKILL_EXISTS'
      });
    }

    const skill = await Skill.create({
      name,
      description,
      category,
      level: level || 'beginner'
    });

    res.status(201).json({
      message: 'Skill created successfully',
      skill
    });
  } catch (error) {
    console.error('Create skill error:', error);
    res.status(500).json({
      message: 'Failed to create skill',
      error: 'CREATE_SKILL_ERROR'
    });
  }
});

// Get skill by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const skill = await Skill.findByPk(id, {
      where: { isActive: true },
      include: [
        {
          model: UserSkill,
          as: 'userSkills',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'firstName', 'lastName', 'profilePicture']
            }
          ]
        }
      ]
    });

    if (!skill) {
      return res.status(404).json({
        message: 'Skill not found',
        error: 'SKILL_NOT_FOUND'
      });
    }

    res.json({
      message: 'Skill retrieved successfully',
      skill
    });
  } catch (error) {
    console.error('Get skill by ID error:', error);
    res.status(500).json({
      message: 'Failed to retrieve skill',
      error: 'SKILL_ERROR'
    });
  }
});

// Add skill to user profile
router.post('/user-skills', authenticateToken, validateUserSkill, async (req, res) => {
  try {
    const {
      skillId,
      proficiencyLevel,
      yearsOfExperience,
      isTeaching,
      isLearning,
      description
    } = req.body;

    // Check if skill exists
    const skill = await Skill.findByPk(skillId);
    if (!skill) {
      return res.status(404).json({
        message: 'Skill not found',
        error: 'SKILL_NOT_FOUND'
      });
    }

    // Check if user already has this skill
    const existingUserSkill = await UserSkill.findOne({
      where: {
        userId: req.user.id,
        skillId
      }
    });

    if (existingUserSkill) {
      return res.status(409).json({
        message: 'You already have this skill in your profile',
        error: 'USER_SKILL_EXISTS'
      });
    }

    const userSkill = await UserSkill.create({
      userId: req.user.id,
      skillId,
      proficiencyLevel,
      yearsOfExperience,
      isTeaching: isTeaching || false,
      isLearning: isLearning || false,
      description
    });

    // Fetch the created user skill with skill details
    const createdUserSkill = await UserSkill.findByPk(userSkill.id, {
      include: [
        {
          model: Skill,
          as: 'skill'
        }
      ]
    });

    res.status(201).json({
      message: 'Skill added to your profile successfully',
      userSkill: createdUserSkill
    });
  } catch (error) {
    console.error('Add user skill error:', error);
    res.status(500).json({
      message: 'Failed to add skill to profile',
      error: 'ADD_USER_SKILL_ERROR'
    });
  }
});

// Get user's skills
router.get('/user-skills/me', authenticateToken, async (req, res) => {
  try {
    const userSkills = await UserSkill.findAll({
      where: {
        userId: req.user.id,
        isActive: true
      },
      include: [
        {
          model: Skill,
          as: 'skill'
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json({
      message: 'User skills retrieved successfully',
      userSkills
    });
  } catch (error) {
    console.error('Get user skills error:', error);
    res.status(500).json({
      message: 'Failed to retrieve user skills',
      error: 'USER_SKILLS_ERROR'
    });
  }
});

// Update user skill
router.put('/user-skills/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      proficiencyLevel,
      yearsOfExperience,
      isTeaching,
      isLearning,
      description
    } = req.body;

    const userSkill = await UserSkill.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!userSkill) {
      return res.status(404).json({
        message: 'User skill not found',
        error: 'USER_SKILL_NOT_FOUND'
      });
    }

    const updateData = {};
    if (proficiencyLevel !== undefined) updateData.proficiencyLevel = proficiencyLevel;
    if (yearsOfExperience !== undefined) updateData.yearsOfExperience = yearsOfExperience;
    if (isTeaching !== undefined) updateData.isTeaching = isTeaching;
    if (isLearning !== undefined) updateData.isLearning = isLearning;
    if (description !== undefined) updateData.description = description;

    await userSkill.update(updateData);

    // Fetch updated user skill with skill details
    const updatedUserSkill = await UserSkill.findByPk(userSkill.id, {
      include: [
        {
          model: Skill,
          as: 'skill'
        }
      ]
    });

    res.json({
      message: 'User skill updated successfully',
      userSkill: updatedUserSkill
    });
  } catch (error) {
    console.error('Update user skill error:', error);
    res.status(500).json({
      message: 'Failed to update user skill',
      error: 'UPDATE_USER_SKILL_ERROR'
    });
  }
});

// Remove user skill
router.delete('/user-skills/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const userSkill = await UserSkill.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!userSkill) {
      return res.status(404).json({
        message: 'User skill not found',
        error: 'USER_SKILL_NOT_FOUND'
      });
    }

    await userSkill.update({ isActive: false });

    res.json({
      message: 'Skill removed from your profile successfully'
    });
  } catch (error) {
    console.error('Remove user skill error:', error);
    res.status(500).json({
      message: 'Failed to remove skill from profile',
      error: 'REMOVE_USER_SKILL_ERROR'
    });
  }
});

module.exports = router;
