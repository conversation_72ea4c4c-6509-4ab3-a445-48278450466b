import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  refreshToken: async () => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
};

// Users API calls
export const usersAPI = {
  getProfile: async () => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  updateProfile: async (profileData) => {
    const response = await api.put('/users/profile', profileData);
    return response.data;
  },

  uploadProfilePicture: async (file) => {
    const formData = new FormData();
    formData.append('profilePicture', file);
    
    const response = await api.post('/users/profile/picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getAllUsers: async (params = {}) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  getUserById: async (userId) => {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },
};

// Skills API calls
export const skillsAPI = {
  getAllSkills: async (params = {}) => {
    const response = await api.get('/skills', { params });
    return response.data;
  },

  getSkillCategories: async () => {
    const response = await api.get('/skills/categories');
    return response.data;
  },

  createSkill: async (skillData) => {
    const response = await api.post('/skills', skillData);
    return response.data;
  },

  getSkillById: async (skillId) => {
    const response = await api.get(`/skills/${skillId}`);
    return response.data;
  },

  addUserSkill: async (userSkillData) => {
    const response = await api.post('/skills/user-skills', userSkillData);
    return response.data;
  },

  getUserSkills: async () => {
    const response = await api.get('/skills/user-skills/me');
    return response.data;
  },

  updateUserSkill: async (userSkillId, updateData) => {
    const response = await api.put(`/skills/user-skills/${userSkillId}`, updateData);
    return response.data;
  },

  removeUserSkill: async (userSkillId) => {
    const response = await api.delete(`/skills/user-skills/${userSkillId}`);
    return response.data;
  },
};

// Exchanges API calls
export const exchangesAPI = {
  getUserExchanges: async (params = {}) => {
    const response = await api.get('/exchanges/me', { params });
    return response.data;
  },

  createExchange: async (exchangeData) => {
    const response = await api.post('/exchanges', exchangeData);
    return response.data;
  },

  getExchangeById: async (exchangeId) => {
    const response = await api.get(`/exchanges/${exchangeId}`);
    return response.data;
  },

  updateExchangeStatus: async (exchangeId, statusData) => {
    const response = await api.patch(`/exchanges/${exchangeId}/status`, statusData);
    return response.data;
  },
};

// Health check
export const healthCheck = async () => {
  const response = await api.get('/health');
  return response.data;
};

export default api;
