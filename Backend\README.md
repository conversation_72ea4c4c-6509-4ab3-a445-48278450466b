# SkillSwap Backend API

A comprehensive backend API for the SkillSwap platform built with Node.js, Express, and Sequelize.

## Features

- **User Authentication**: Registration, login, JWT-based authentication
- **User Management**: Profile management, profile picture upload
- **Skill Management**: Create, manage, and categorize skills
- **User Skills**: Add skills to user profiles with proficiency levels
- **Skill Exchange**: Request and manage skill exchanges between users
- **Security**: Rate limiting, CORS, input validation, password hashing
- **File Upload**: Profile picture upload with validation

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Security**: Helmet, CORS, bcryptjs, express-rate-limit
- **Validation**: express-validator
- **File Upload**: Multer

## Project Structure

```
Backend/
├── config/
│   └── database.js          # Database configuration
├── middleware/
│   ├── auth.js              # Authentication middleware
│   └── validation.js        # Input validation middleware
├── models/
│   ├── User.js              # User model
│   ├── Skill.js             # Skill model
│   ├── UserSkill.js         # User-Skill relationship model
│   ├── SkillExchange.js     # Skill exchange model
│   └── index.js             # Model associations
├── routes/
│   ├── auth.js              # Authentication routes
│   ├── users.js             # User management routes
│   ├── skills.js            # Skill management routes
│   └── exchanges.js         # Skill exchange routes
├── uploads/
│   └── profiles/            # Profile picture uploads
├── .env                     # Environment variables
├── server.js                # Main server file
└── package.json
```

## Installation

1. **Clone the repository**
   ```bash
   cd SkillSwap/Backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file with the following variables:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASS=@Mysql.com321$#@
   DB_NAME=SkillSwap1
   DB_PORT=3306
   PORT=3001
   NODE_ENV=development
   JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
   JWT_EXPIRES_IN=7d
   FRONTEND_URL=http://localhost:5173
   MAX_FILE_SIZE=5242880
   UPLOAD_PATH=./uploads
   ```

4. **Set up MySQL database**
   - Create a MySQL database named `SkillSwap1`
   - Ensure MySQL is running on localhost:3306

5. **Start the server**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Authentication (`/api/auth`)
- `POST /register` - Register new user
- `POST /login` - User login
- `GET /me` - Get current user profile
- `POST /refresh` - Refresh JWT token
- `POST /logout` - User logout

### Users (`/api/users`)
- `GET /profile` - Get user profile with skills
- `PUT /profile` - Update user profile
- `POST /profile/picture` - Upload profile picture
- `GET /` - Get all users (with pagination)
- `GET /:id` - Get user by ID

### Skills (`/api/skills`)
- `GET /` - Get all skills (with pagination and filters)
- `GET /categories` - Get skill categories
- `POST /` - Create new skill
- `GET /:id` - Get skill by ID
- `POST /user-skills` - Add skill to user profile
- `GET /user-skills/me` - Get current user's skills
- `PUT /user-skills/:id` - Update user skill
- `DELETE /user-skills/:id` - Remove user skill

### Exchanges (`/api/exchanges`)
- `GET /me` - Get user's exchanges
- `POST /` - Create skill exchange request
- `GET /:id` - Get exchange by ID
- `PATCH /:id/status` - Update exchange status

## Database Models

### User
- Personal information (name, email, phone, etc.)
- Authentication data (password, tokens)
- Profile data (bio, location, profile picture)

### Skill
- Skill information (name, description, category, level)
- Categorized skills for easy browsing

### UserSkill
- Many-to-many relationship between users and skills
- Proficiency level, experience, teaching/learning preferences

### SkillExchange
- Skill exchange requests between users
- Status tracking, scheduling, ratings, and feedback

## Security Features

- **Password Hashing**: bcryptjs with salt rounds
- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Prevents abuse and DDoS attacks
- **Input Validation**: Comprehensive validation using express-validator
- **CORS**: Configured for frontend integration
- **Helmet**: Security headers for protection
- **File Upload Security**: File type and size validation

## Development

### Running in Development Mode
```bash
npm run dev
```

### Database Sync
The application automatically syncs database models on startup using `sequelize.sync({ alter: true })`.

### Environment Variables
All sensitive configuration is stored in environment variables. Never commit the `.env` file to version control.

## API Response Format

### Success Response
```json
{
  "message": "Operation successful",
  "data": { ... },
  "user": { ... },
  "token": "jwt_token_here"
}
```

### Error Response
```json
{
  "message": "Error description",
  "error": "ERROR_CODE",
  "errors": [
    {
      "field": "fieldName",
      "message": "Validation error message",
      "value": "invalid_value"
    }
  ]
}
```

## Contributing

1. Follow the existing code structure and naming conventions
2. Add proper validation for all inputs
3. Include error handling for all operations
4. Update this README when adding new features
5. Test all endpoints before committing

## License

ISC License
