import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faEye,
  faPlus,
  faSort,
  faSortUp,
  faSortDown,
  faCheckCircle,
  faTimesCircle,
  faGraduationCap,
  faCode,
  faPalette,
  faMusic,
  faLanguage,
  faHeartbeat,
  faUtensils,
  faChartLine
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const SkillsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 1.8rem;
  color: #333;
  margin: 0;
`;

const ActionButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #5a3cc0;
  }
`;

const FilterSection = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const SearchInput = styled.div`
  flex: 1;
  position: relative;
  
  input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
    }
  }
  
  svg {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
  }
`;

const FilterButton = styled.button`
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
`;

const CategoriesContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const CategoryButton = styled.button`
  background-color: ${props => props.active ? 'var(--primary-color)' : 'white'};
  color: ${props => props.active ? 'white' : '#555'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : '#ddd'};
  border-radius: 50px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : '#f8f9fa'};
  }
`;

const TableCard = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  background-color: #f8f9fa;
  
  th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
  }
`;

const TableBody = styled.tbody`
  tr {
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
  }
  
  td {
    padding: 15px;
    color: #555;
  }
`;

const SortIcon = styled.span`
  margin-left: 5px;
  display: inline-block;
`;

const SkillInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const SkillIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: ${props => props.bgColor || '#f1f3f5'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
`;

const SkillName = styled.div`
  font-weight: 500;
  color: #333;
`;

const SkillCategory = styled.div`
  font-size: 0.8rem;
  color: #777;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  
  background-color: ${props => {
    switch(props.status) {
      case 'active': return '#d4edda';
      case 'inactive': return '#f8d7da';
      case 'pending': return '#fff3cd';
      default: return '#e2e3e5';
    }
  }};
  
  color: ${props => {
    switch(props.status) {
      case 'active': return '#155724';
      case 'inactive': return '#721c24';
      case 'pending': return '#856404';
      default: return '#383d41';
    }
  }};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.color || '#6c757d'};
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    color: ${props => {
      switch(props.color) {
        case '#dc3545': return '#c82333';
        case '#28a745': return '#218838';
        case '#007bff': return '#0069d9';
        default: return '#5a6268';
      }
    }};
  }
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-top: 1px solid #eee;
`;

const PageInfo = styled.div`
  font-size: 0.9rem;
  color: #6c757d;
`;

const PageButtons = styled.div`
  display: flex;
  gap: 5px;
`;

const PageButton = styled.button`
  background-color: ${props => props.active ? 'var(--primary-color)' : 'white'};
  color: ${props => props.active ? 'white' : '#6c757d'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : '#ddd'};
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : '#f8f9fa'};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Skills = () => {
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Categories
  const categories = [
    { id: 'all', name: 'All Categories', icon: faGraduationCap },
    { id: 'programming', name: 'Programming', icon: faCode },
    { id: 'design', name: 'Design', icon: faPalette },
    { id: 'business', name: 'Business', icon: faChartLine },
    { id: 'music', name: 'Music', icon: faMusic },
    { id: 'language', name: 'Language', icon: faLanguage },
    { id: 'health', name: 'Health & Fitness', icon: faHeartbeat },
    { id: 'cooking', name: 'Cooking', icon: faUtensils }
  ];
  
  // Mock data for skills
  const skills = [
    {
      id: 1,
      name: 'JavaScript Programming',
      category: 'programming',
      status: 'active',
      users: 1245,
      createdDate: '2023-01-10',
      icon: faCode,
      color: '#f0db4f'
    },
    {
      id: 2,
      name: 'UI/UX Design',
      category: 'design',
      status: 'active',
      users: 876,
      createdDate: '2023-02-15',
      icon: faPalette,
      color: '#ff7eb9'
    },
    {
      id: 3,
      name: 'Digital Marketing',
      category: 'business',
      status: 'active',
      users: 654,
      createdDate: '2023-03-20',
      icon: faChartLine,
      color: '#7eb0ff'
    },
    {
      id: 4,
      name: 'Piano Lessons',
      category: 'music',
      status: 'inactive',
      users: 432,
      createdDate: '2023-04-25',
      icon: faMusic,
      color: '#b57edc'
    },
    {
      id: 5,
      name: 'Spanish Language',
      category: 'language',
      status: 'active',
      users: 789,
      createdDate: '2023-05-30',
      icon: faLanguage,
      color: '#ff9e7e'
    },
    {
      id: 6,
      name: 'Yoga Instruction',
      category: 'health',
      status: 'pending',
      users: 321,
      createdDate: '2023-06-05',
      icon: faHeartbeat,
      color: '#7eff8e'
    },
    {
      id: 7,
      name: 'Italian Cooking',
      category: 'cooking',
      status: 'active',
      users: 567,
      createdDate: '2023-07-10',
      icon: faUtensils,
      color: '#ff7e7e'
    },
    {
      id: 8,
      name: 'Python Programming',
      category: 'programming',
      status: 'active',
      users: 987,
      createdDate: '2023-08-15',
      icon: faCode,
      color: '#4b8bbe'
    }
  ];
  
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  const getSortIcon = (field) => {
    if (sortField !== field) return <FontAwesomeIcon icon={faSort} />;
    return sortDirection === 'asc' ? <FontAwesomeIcon icon={faSortUp} /> : <FontAwesomeIcon icon={faSortDown} />;
  };
  
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Filter skills by category
  const filteredSkills = activeCategory === 'all' 
    ? skills 
    : skills.filter(skill => skill.category === activeCategory);
  
  return (
    <SkillsContainer>
      <PageHeader>
        <PageTitle>Skills Management</PageTitle>
        <ActionButton>
          <FontAwesomeIcon icon={faPlus} />
          Add New Skill
        </ActionButton>
      </PageHeader>
      
      <FilterSection>
        <SearchInput>
          <FontAwesomeIcon icon={faSearch} />
          <input type="text" placeholder="Search skills..." />
        </SearchInput>
        <FilterButton>
          <FontAwesomeIcon icon={faFilter} />
          Filters
        </FilterButton>
      </FilterSection>
      
      <CategoriesContainer>
        {categories.map(category => (
          <CategoryButton
            key={category.id}
            active={activeCategory === category.id}
            onClick={() => setActiveCategory(category.id)}
          >
            <FontAwesomeIcon icon={category.icon} />
            {category.name}
          </CategoryButton>
        ))}
      </CategoriesContainer>
      
      <TableCard>
        <Table>
          <TableHead>
            <tr>
              <th onClick={() => handleSort('name')}>
                Skill
                <SortIcon>{getSortIcon('name')}</SortIcon>
              </th>
              <th onClick={() => handleSort('category')}>
                Category
                <SortIcon>{getSortIcon('category')}</SortIcon>
              </th>
              <th onClick={() => handleSort('status')}>
                Status
                <SortIcon>{getSortIcon('status')}</SortIcon>
              </th>
              <th onClick={() => handleSort('users')}>
                Users
                <SortIcon>{getSortIcon('users')}</SortIcon>
              </th>
              <th onClick={() => handleSort('createdDate')}>
                Created Date
                <SortIcon>{getSortIcon('createdDate')}</SortIcon>
              </th>
              <th>Actions</th>
            </tr>
          </TableHead>
          <TableBody>
            {filteredSkills.map(skill => (
              <tr key={skill.id}>
                <td>
                  <SkillInfo>
                    <SkillIcon bgColor={skill.color}>
                      <FontAwesomeIcon icon={skill.icon} />
                    </SkillIcon>
                    <SkillName>{skill.name}</SkillName>
                  </SkillInfo>
                </td>
                <td>
                  <SkillCategory>
                    {skill.category.charAt(0).toUpperCase() + skill.category.slice(1)}
                  </SkillCategory>
                </td>
                <td>
                  <StatusBadge status={skill.status}>
                    {skill.status === 'active' && <FontAwesomeIcon icon={faCheckCircle} style={{ marginRight: '5px' }} />}
                    {skill.status === 'inactive' && <FontAwesomeIcon icon={faTimesCircle} style={{ marginRight: '5px' }} />}
                    {skill.status.charAt(0).toUpperCase() + skill.status.slice(1)}
                  </StatusBadge>
                </td>
                <td>{skill.users.toLocaleString()}</td>
                <td>{formatDate(skill.createdDate)}</td>
                <td>
                  <ActionButtons>
                    <IconButton color="#007bff" title="View">
                      <FontAwesomeIcon icon={faEye} />
                    </IconButton>
                    <IconButton color="#28a745" title="Edit">
                      <FontAwesomeIcon icon={faEdit} />
                    </IconButton>
                    <IconButton color="#dc3545" title="Delete">
                      <FontAwesomeIcon icon={faTrash} />
                    </IconButton>
                  </ActionButtons>
                </td>
              </tr>
            ))}
          </TableBody>
        </Table>
        <Pagination>
          <PageInfo>Showing 1 to {filteredSkills.length} of {filteredSkills.length} entries</PageInfo>
          <PageButtons>
            <PageButton disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
              Previous
            </PageButton>
            <PageButton active={currentPage === 1} onClick={() => setCurrentPage(1)}>
              1
            </PageButton>
            <PageButton onClick={() => setCurrentPage(2)}>
              2
            </PageButton>
            <PageButton onClick={() => setCurrentPage(3)}>
              3
            </PageButton>
            <PageButton onClick={() => setCurrentPage(currentPage + 1)}>
              Next
            </PageButton>
          </PageButtons>
        </Pagination>
      </TableCard>
    </SkillsContainer>
  );
};

export default Skills;
