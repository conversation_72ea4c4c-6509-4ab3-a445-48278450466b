const express = require('express');
const { Op } = require('sequelize');
const { SkillExchange, User, Skill } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get all exchanges for current user
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const { status, type = 'all' } = req.query;

    let whereClause = {};
    
    if (type === 'requested') {
      whereClause.requesterId = req.user.id;
    } else if (type === 'provided') {
      whereClause.providerId = req.user.id;
    } else {
      whereClause[Op.or] = [
        { requesterId: req.user.id },
        { providerId: req.user.id }
      ];
    }

    if (status) {
      whereClause.status = status;
    }

    const exchanges = await SkillExchange.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: User,
          as: 'provider',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: Skill,
          as: 'requestedSkill',
          attributes: ['id', 'name', 'category']
        },
        {
          model: Skill,
          as: 'offeredSkill',
          attributes: ['id', 'name', 'category']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json({
      message: 'Exchanges retrieved successfully',
      exchanges
    });
  } catch (error) {
    console.error('Get user exchanges error:', error);
    res.status(500).json({
      message: 'Failed to retrieve exchanges',
      error: 'EXCHANGES_ERROR'
    });
  }
});

// Create new skill exchange request
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      providerId,
      requestedSkillId,
      offeredSkillId,
      title,
      description,
      duration,
      scheduledAt
    } = req.body;

    // Validation
    if (!providerId || !requestedSkillId || !offeredSkillId || !title) {
      return res.status(400).json({
        message: 'Provider ID, requested skill ID, offered skill ID, and title are required',
        error: 'MISSING_REQUIRED_FIELDS'
      });
    }

    if (providerId === req.user.id) {
      return res.status(400).json({
        message: 'You cannot create an exchange request with yourself',
        error: 'INVALID_PROVIDER'
      });
    }

    // Check if provider exists
    const provider = await User.findByPk(providerId);
    if (!provider) {
      return res.status(404).json({
        message: 'Provider not found',
        error: 'PROVIDER_NOT_FOUND'
      });
    }

    // Check if skills exist
    const [requestedSkill, offeredSkill] = await Promise.all([
      Skill.findByPk(requestedSkillId),
      Skill.findByPk(offeredSkillId)
    ]);

    if (!requestedSkill || !offeredSkill) {
      return res.status(404).json({
        message: 'One or both skills not found',
        error: 'SKILLS_NOT_FOUND'
      });
    }

    const exchange = await SkillExchange.create({
      requesterId: req.user.id,
      providerId,
      requestedSkillId,
      offeredSkillId,
      title,
      description,
      duration,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : null
    });

    // Fetch the created exchange with all details
    const createdExchange = await SkillExchange.findByPk(exchange.id, {
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: User,
          as: 'provider',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: Skill,
          as: 'requestedSkill',
          attributes: ['id', 'name', 'category']
        },
        {
          model: Skill,
          as: 'offeredSkill',
          attributes: ['id', 'name', 'category']
        }
      ]
    });

    res.status(201).json({
      message: 'Skill exchange request created successfully',
      exchange: createdExchange
    });
  } catch (error) {
    console.error('Create exchange error:', error);
    res.status(500).json({
      message: 'Failed to create exchange request',
      error: 'CREATE_EXCHANGE_ERROR'
    });
  }
});

// Get exchange by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const exchange = await SkillExchange.findByPk(id, {
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture', 'email']
        },
        {
          model: User,
          as: 'provider',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture', 'email']
        },
        {
          model: Skill,
          as: 'requestedSkill'
        },
        {
          model: Skill,
          as: 'offeredSkill'
        }
      ]
    });

    if (!exchange) {
      return res.status(404).json({
        message: 'Exchange not found',
        error: 'EXCHANGE_NOT_FOUND'
      });
    }

    // Check if user is involved in this exchange
    if (exchange.requesterId !== req.user.id && exchange.providerId !== req.user.id) {
      return res.status(403).json({
        message: 'You are not authorized to view this exchange',
        error: 'UNAUTHORIZED'
      });
    }

    res.json({
      message: 'Exchange retrieved successfully',
      exchange
    });
  } catch (error) {
    console.error('Get exchange error:', error);
    res.status(500).json({
      message: 'Failed to retrieve exchange',
      error: 'EXCHANGE_ERROR'
    });
  }
});

// Update exchange status
router.patch('/:id/status', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, feedback, rating } = req.body;

    if (!status) {
      return res.status(400).json({
        message: 'Status is required',
        error: 'MISSING_STATUS'
      });
    }

    const validStatuses = ['pending', 'accepted', 'rejected', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        message: 'Invalid status',
        error: 'INVALID_STATUS'
      });
    }

    const exchange = await SkillExchange.findByPk(id);
    if (!exchange) {
      return res.status(404).json({
        message: 'Exchange not found',
        error: 'EXCHANGE_NOT_FOUND'
      });
    }

    // Check authorization based on status change
    if (status === 'accepted' || status === 'rejected') {
      // Only provider can accept or reject
      if (exchange.providerId !== req.user.id) {
        return res.status(403).json({
          message: 'Only the provider can accept or reject this exchange',
          error: 'UNAUTHORIZED'
        });
      }
    } else if (status === 'cancelled') {
      // Either party can cancel
      if (exchange.requesterId !== req.user.id && exchange.providerId !== req.user.id) {
        return res.status(403).json({
          message: 'You are not authorized to cancel this exchange',
          error: 'UNAUTHORIZED'
        });
      }
    } else if (status === 'completed') {
      // Either party can mark as completed
      if (exchange.requesterId !== req.user.id && exchange.providerId !== req.user.id) {
        return res.status(403).json({
          message: 'You are not authorized to complete this exchange',
          error: 'UNAUTHORIZED'
        });
      }
    }

    const updateData = { status };
    
    if (status === 'completed') {
      updateData.completedAt = new Date();
      if (feedback) updateData.feedback = feedback;
      if (rating) updateData.rating = rating;
    }

    await exchange.update(updateData);

    // Fetch updated exchange with details
    const updatedExchange = await SkillExchange.findByPk(exchange.id, {
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: User,
          as: 'provider',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: Skill,
          as: 'requestedSkill',
          attributes: ['id', 'name', 'category']
        },
        {
          model: Skill,
          as: 'offeredSkill',
          attributes: ['id', 'name', 'category']
        }
      ]
    });

    res.json({
      message: 'Exchange status updated successfully',
      exchange: updatedExchange
    });
  } catch (error) {
    console.error('Update exchange status error:', error);
    res.status(500).json({
      message: 'Failed to update exchange status',
      error: 'UPDATE_STATUS_ERROR'
    });
  }
});

module.exports = router;
