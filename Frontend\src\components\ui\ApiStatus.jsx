import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { healthCheck } from '../../services/api';

const StatusContainer = styled.div`
  position: fixed;
  top: 10px;
  right: 10px;
  background: ${props => props.connected ? '#4caf50' : '#f44336'};
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
`;

const StatusDot = styled.span`
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  margin-right: 6px;
  animation: ${props => props.connected ? 'none' : 'blink 1s infinite'};

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
  }
`;

const ApiStatus = () => {
  const [connected, setConnected] = useState(false);
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    const checkConnection = async () => {
      try {
        await healthCheck();
        setConnected(true);
      } catch (error) {
        setConnected(false);
        console.warn('Backend connection failed:', error.message);
      } finally {
        setChecking(false);
      }
    };

    // Check immediately
    checkConnection();

    // Check every 30 seconds
    const interval = setInterval(checkConnection, 30000);

    return () => clearInterval(interval);
  }, []);

  if (checking) {
    return (
      <StatusContainer connected={false}>
        <StatusDot connected={false} />
        Checking API...
      </StatusContainer>
    );
  }

  return (
    <StatusContainer connected={connected}>
      <StatusDot connected={connected} />
      {connected ? 'API Connected' : 'API Disconnected'}
    </StatusContainer>
  );
};

export default ApiStatus;
