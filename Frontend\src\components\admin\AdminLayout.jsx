import { useState } from 'react';
import { Outlet, Link, useLocation, Navigate, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import UserDropdown from '../ui/UserDropdown';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faGraduationCap,
  faExchangeAlt,
  faBars,
  faTimes,
  faSignOutAlt,
  faBell,
  faUser
} from '@fortawesome/free-solid-svg-icons';

const AdminContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
`;

const Sidebar = styled.aside`
  width: 250px;
  background-color: #212529;
  color: #fff;
  position: fixed;
  height: 100vh;
  transition: all 0.3s ease;
  z-index: 1000;
  left: ${props => (props.isOpen ? '0' : '-250px')};

  @media (max-width: 768px) {
    left: ${props => (props.isOpen ? '0' : '-250px')};
  }
`;

const SidebarHeader = styled.div`
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Logo = styled(Link)`
  color: #fff;
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
  display: flex;
  align-items: center;

  span {
    color: var(--primary-color);
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
`;

const SidebarNav = styled.nav`
  padding: 20px 0;
`;

const NavItem = styled(Link)`
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
  }

  &.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-left-color: var(--primary-color);
  }

  svg {
    margin-right: 10px;
    width: 20px;
  }
`;

const MainContent = styled.main`
  flex: 1;
  margin-left: ${props => (props.sidebarOpen ? '250px' : '0')};
  transition: all 0.3s ease;
  width: ${props => (props.sidebarOpen ? 'calc(100% - 250px)' : '100%')};

  @media (max-width: 768px) {
    margin-left: 0;
    width: 100%;
  }
`;

const Header = styled.header`
  background-color: #fff;
  padding: 15px 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
`;

const MenuToggle = styled.button`
  background: none;
  border: none;
  color: #333;
  font-size: 1.2rem;
  cursor: pointer;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
`;

const HeaderActions = styled.div`
  display: flex;
  align-items: center;
`;

const HeaderButton = styled.button`
  background: none;
  border: none;
  color: #555;
  font-size: 1.1rem;
  margin-left: 15px;
  cursor: pointer;
  position: relative;

  &:hover {
    color: var(--primary-color);
  }
`;

const NotificationBadge = styled.span`
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  padding: 20px;
`;

const AdminLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { currentUser, isAdmin, logout, loading } = useAuth();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!currentUser || !isAdmin()) {
    return <Navigate to="/login" replace />;
  }

  return (
    <AdminContainer>
      <Sidebar isOpen={sidebarOpen}>
        <SidebarHeader>
          <Logo to="/admin">
            Skill<span>Swap</span> Admin
          </Logo>
          <CloseButton onClick={toggleSidebar}>
            <FontAwesomeIcon icon={faTimes} />
          </CloseButton>
        </SidebarHeader>
        <SidebarNav>
          <NavItem
            to="/admin/users"
            className={location.pathname === '/admin' || location.pathname.startsWith('/admin/users') ? 'active' : ''}
          >
            <FontAwesomeIcon icon={faUsers} />
            Users
          </NavItem>
          <NavItem
            to="/admin/skills"
            className={location.pathname.startsWith('/admin/skills') ? 'active' : ''}
          >
            <FontAwesomeIcon icon={faGraduationCap} />
            Skills
          </NavItem>
          <NavItem
            to="/admin/exchanges"
            className={location.pathname.startsWith('/admin/exchanges') ? 'active' : ''}
          >
            <FontAwesomeIcon icon={faExchangeAlt} />
            Exchanges
          </NavItem>
        </SidebarNav>
      </Sidebar>

      <MainContent sidebarOpen={sidebarOpen}>
        <Header>
          <MenuToggle onClick={toggleSidebar}>
            <FontAwesomeIcon icon={faBars} />
          </MenuToggle>
          <HeaderActions>
            <HeaderButton>
              <FontAwesomeIcon icon={faBell} />
              <NotificationBadge>3</NotificationBadge>
            </HeaderButton>
            <UserDropdown user={currentUser} onLogout={handleLogout} />
          </HeaderActions>
        </Header>
        <ContentWrapper>
          <Outlet />
        </ContentWrapper>
      </MainContent>
    </AdminContainer>
  );
};

export default AdminLayout;
