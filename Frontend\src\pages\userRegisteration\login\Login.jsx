import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { useAuth } from "../../../context/AuthContext";
import styled from "styled-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUser, faLock, faSignInAlt } from "@fortawesome/free-solid-svg-icons";

// Styled Components
const LoginContainer = styled.div`
  background-color: #212121; // Dark background color
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`;

const LoginTitle = styled.h1`
  color: white;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
`;

const LoginForm = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
`;

const InputWrapper = styled.div`
  position: relative;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
`;

const InputIcon = styled.span`
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #777;
`;

const ButtonsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
`;

const PrimaryButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover {
    background-color: #5a3cc0;
  }
`;

const AdminButton = styled(PrimaryButton)`
  background-color: #4db6ac;

  &:hover {
    background-color: #3a9c92;
  }
`;

const LinkContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  font-size: 0.9rem;
`;

const StyledLink = styled(Link)`
  color: var(--primary-color);
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const ErrorMessage = styled.div`
  color: #e53935;
  font-size: 0.9rem;
  margin-top: 0.5rem;
`;

const Login = () => {
  const navigate = useNavigate();
  const { login, currentUser } = useAuth();
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [error, setError] = useState("");

  useEffect(() => {
    // If user is already logged in, redirect to home page
    if (currentUser) {
      if (currentUser.username === "admin") {
        navigate("/admin");
      } else {
        navigate("/home");
      }
    }
  }, [currentUser, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleLogin = (e) => {
    e.preventDefault();

    // Basic validation
    if (!formData.username || !formData.password) {
      setError("Please enter both username and password");
      return;
    }

    // Regular user login would be handled here with API calls
    console.log("Login attempt:", formData);

    // For now, just log in the user with the provided credentials
    login({
      username: formData.username,
      name: formData.username, // Using username as name for simplicity
      isAuthenticated: true,
    });

    // Navigation will happen in the useEffect hook
  };

  const handleAdminLogin = (e) => {
    e.preventDefault();

    // Check if credentials match admin credentials
    if (formData.username === "admin" && formData.password === "admin12") {
      // Log in as admin
      login({
        username: "admin",
        name: "Administrator",
        isAuthenticated: true,
        isAdmin: true,
      });
      // Navigation will happen in the useEffect hook
    } else {
      setError("Invalid admin credentials");
    }
  };

  return (
    <LoginContainer>
      <LoginTitle>
        Welcome to{" "}
        <span style={{ color: "var(--primary-color)" }}>SkillSwap</span>
      </LoginTitle>

      <LoginForm>
        <form onSubmit={handleLogin}>
          <FormGroup>
            <Label>Username</Label>
            <InputWrapper>
              <InputIcon>
                <FontAwesomeIcon icon={faUser} />
              </InputIcon>
              <Input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter your username"
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <Label>Password</Label>
            <InputWrapper>
              <InputIcon>
                <FontAwesomeIcon icon={faLock} />
              </InputIcon>
              <Input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
              />
            </InputWrapper>
          </FormGroup>

          {error && <ErrorMessage>{error}</ErrorMessage>}

          <ButtonsContainer>
            <PrimaryButton type="submit">
              <FontAwesomeIcon icon={faSignInAlt} />
              Login
            </PrimaryButton>
            <AdminButton type="button" onClick={handleAdminLogin}>
              <FontAwesomeIcon icon={faSignInAlt} />
              Login as Admin
            </AdminButton>
          </ButtonsContainer>

          <LinkContainer>
            <StyledLink to="/create-account">Create an account</StyledLink>
            <StyledLink to="/">Forgot password?</StyledLink>
          </LinkContainer>
        </form>
      </LoginForm>
    </LoginContainer>
  );
};

export default Login;
