import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { ThemeProvider } from "styled-components";
import { AuthProvider } from "./context/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import GlobalStyles from "./styles/GlobalStyles";
import theme from "./styles/theme";
import ApiStatus from "./components/ui/ApiStatus";
import IntegrationTest from "./components/ui/IntegrationTest";

// Layout
import Layout from "./components/layout/Layout";
import AdminLayout from "./components/admin";

// Pages
import Home from "./pages/home";
import About from "./pages/about";
import Services from "./pages/services";
import Discover from "./pages/discover";
import Contact from "./pages/contact";
import Profile from "./pages/profile";
import UserDetails from "./pages/userDetails";
import { Login, SignUp } from "./pages/userRegisteration";
import Messages from "./pages/messages";
import Schedule from "./pages/schedule";
import UserSettings from "./pages/settings";
import UserDashboard from "./pages/dashboard";

// Admin Pages
import { Users, Skills, Exchanges } from "./pages/admin";

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <AuthProvider>
        <ApiStatus />
        <IntegrationTest />
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/create-account" element={<SignUp />} />

            {/* Redirect root to login */}
            <Route path="/" element={<Navigate to="/login" replace />} exact />

            {/* Main Routes with Layout - Protected */}
            <Route element={<ProtectedRoute />}>
              <Route element={<Layout />}>
                <Route path="/home" element={<Home />} />
                <Route path="/about" element={<About />} />
                <Route path="/services" element={<Services />} />
                <Route path="/discover" element={<Discover />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/dashboard" element={<UserDashboard />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/profile/:userId" element={<Profile />} />
                <Route path="/register" element={<UserDetails />} />
                <Route path="/messages" element={<Messages />} />
                <Route path="/messages/:userId" element={<Messages />} />
                <Route path="/schedule" element={<Schedule />} />
                <Route path="/schedule/:userId" element={<Schedule />} />
                <Route path="/settings" element={<UserSettings />} />
              </Route>
            </Route>

            {/* Admin Routes - Protected and Admin Only */}
            <Route element={<ProtectedRoute requireAdmin />}>
              <Route path="/admin" element={<AdminLayout />}>
                <Route index element={<Users />} />
                <Route path="users" element={<Users />} />
                <Route path="skills" element={<Skills />} />
                <Route path="exchanges" element={<Exchanges />} />
              </Route>
            </Route>
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
