# SkillSwap Backend Setup Guide

## Prerequisites

1. **Node.js** (v16 or higher)
2. **MySQL** (v8.0 or higher)
3. **npm** (comes with Node.js)

## MySQL Setup

### Option 1: Install MySQL Locally

1. **Download and Install MySQL**
   - Download from: https://dev.mysql.com/downloads/mysql/
   - Follow the installation wizard
   - Remember the root password you set during installation

2. **Start MySQL Service**
   ```bash
   # Windows (as Administrator)
   net start mysql
   
   # macOS
   sudo /usr/local/mysql/support-files/mysql.server start
   
   # Linux
   sudo systemctl start mysql
   ```

3. **Test MySQL Connection**
   ```bash
   mysql -u root -p
   ```
   Enter your root password when prompted.

### Option 2: Use XAMPP (Easier for Development)

1. **Download XAMPP**
   - Download from: https://www.apachefriends.org/
   - Install XAMPP

2. **Start MySQL from XAMPP Control Panel**
   - Open XAMPP Control Panel
   - Click "Start" next to MySQL
   - Default credentials: username=`root`, password=`` (empty)

### Option 3: Use Docker

```bash
# Run MySQL in Docker
docker run --name skillswap-mysql \
  -e MYSQL_ROOT_PASSWORD=your_password \
  -e MYSQL_DATABASE=SkillSwap1 \
  -p 3306:3306 \
  -d mysql:8.0

# Connect to MySQL
docker exec -it skillswap-mysql mysql -u root -p
```

## Backend Setup

1. **Navigate to Backend Directory**
   ```bash
   cd SkillSwap/Backend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Configure Environment Variables**
   ```bash
   # Copy the example environment file
   cp .env.example .env
   
   # Edit .env file with your MySQL credentials
   ```

   Update the `.env` file with your MySQL credentials:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASS=your_mysql_password
   DB_NAME=SkillSwap1
   DB_PORT=3306
   ```

4. **Test Database Connection**
   ```bash
   node test-db.js
   ```
   This will test the connection and create the database if it doesn't exist.

5. **Start the Development Server**
   ```bash
   npm run dev
   ```

## Common Issues and Solutions

### Issue 1: Access Denied Error
```
Error: Access denied for user 'root'@'localhost' (using password: YES)
```

**Solutions:**
1. Check if the password in `.env` is correct
2. Try connecting with MySQL client: `mysql -u root -p`
3. Reset MySQL root password if needed
4. If using XAMPP, the default password is usually empty

### Issue 2: Connection Refused
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```

**Solutions:**
1. Make sure MySQL service is running
2. Check if MySQL is listening on port 3306
3. Verify the host and port in `.env` file

### Issue 3: Database Doesn't Exist
The `test-db.js` script will automatically create the database if it doesn't exist.

### Issue 4: Permission Issues
If you get permission errors, you might need to grant privileges:

```sql
-- Connect to MySQL as root
mysql -u root -p

-- Grant all privileges to root user
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
FLUSH PRIVILEGES;

-- Or create a new user specifically for the application
CREATE USER 'skillswap'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON SkillSwap1.* TO 'skillswap'@'localhost';
FLUSH PRIVILEGES;
```

## Verification

Once everything is set up correctly, you should see:

```
Database connection established successfully.
Database models synchronized.
Server is running on port 3001
Environment: development
```

You can test the API by visiting: http://localhost:3001/api/health

## Next Steps

1. The backend will automatically create all necessary database tables
2. You can start testing the API endpoints
3. The frontend can now connect to this backend
4. Check the main README.md for API documentation

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `DB_HOST` | MySQL host | `localhost` |
| `DB_USER` | MySQL username | `root` |
| `DB_PASS` | MySQL password | `your_password` |
| `DB_NAME` | Database name | `SkillSwap1` |
| `DB_PORT` | MySQL port | `3306` |
| `PORT` | Server port | `3001` |
| `NODE_ENV` | Environment | `development` |
| `JWT_SECRET` | JWT secret key | `your_secret_key` |
| `JWT_EXPIRES_IN` | Token expiration | `7d` |
| `FRONTEND_URL` | Frontend URL for CORS | `http://localhost:5173` |
| `MAX_FILE_SIZE` | Max upload size in bytes | `5242880` (5MB) |
| `UPLOAD_PATH` | Upload directory | `./uploads` |
